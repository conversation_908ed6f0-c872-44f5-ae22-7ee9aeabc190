{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Navbar\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext, useRef, useEffect } from 'react';\nimport './Navbar.css';\nimport logo from '../Asset/logo1.png';\nimport cart_icon from '../Asset/cart_icon.png';\nimport { Link, useLocation } from 'react-router-dom';\nimport { ShopContext } from '../../Context/ShopContext';\nimport nav_dropdown from '../Asset/dropdown_icon.png';\nimport { FaHeart, FaSearch, FaMoon, FaSun, FaUser } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const [menu, setMenu] = useState(\"shop\");\n  const [darkMode, setDarkMode] = useState(false);\n  const [showSearch, setShowSearch] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [wishlistCount, setWishlistCount] = useState(0);\n  const {\n    getTotalCartItems\n  } = useContext(ShopContext);\n  const menuRef = useRef();\n  const location = useLocation();\n  useEffect(() => {\n    // Set active menu based on current path\n    const path = location.pathname;\n    if (path === '/') setMenu(\"shop\");else if (path === '/mens') setMenu(\"Men\");else if (path === '/womens') setMenu(\"Women\");else if (path === '/kids') setMenu(\"Kids\");else if (path === '/wishlist') setMenu(\"wishlist\");\n\n    // Load dark mode preference\n    const savedDarkMode = localStorage.getItem('darkMode') === 'true';\n    setDarkMode(savedDarkMode);\n\n    // Apply dark mode to body\n    if (savedDarkMode) {\n      document.body.classList.add('dark-mode');\n    }\n\n    // Fetch wishlist count\n    fetchWishlistCount();\n  }, [location]);\n  const fetchWishlistCount = async () => {\n    if (localStorage.getItem('auth-token')) {\n      try {\n        const response = await fetch('http://localhost:3000/getwishlist', {\n          method: 'POST',\n          headers: {\n            Accept: 'application/json',\n            'auth-token': `${localStorage.getItem('auth-token')}`,\n            'Content-Type': 'application/json'\n          },\n          body: \"\"\n        });\n        const data = await response.json();\n        setWishlistCount(data.length || 0);\n      } catch (error) {\n        console.error('Error fetching wishlist:', error);\n      }\n    }\n  };\n  const dropdownToggle = e => {\n    menuRef.current.classList.toggle('nav-menu-visible');\n    e.target.classList.toggle('open');\n  };\n  const toggleDarkMode = () => {\n    const newDarkMode = !darkMode;\n    setDarkMode(newDarkMode);\n    localStorage.setItem('darkMode', newDarkMode);\n    if (newDarkMode) {\n      document.body.classList.add('dark-mode');\n    } else {\n      document.body.classList.remove('dark-mode');\n    }\n  };\n  const handleSearch = e => {\n    e.preventDefault();\n    if (searchQuery.trim()) {\n      // Navigate to search results page or filter current page\n      window.location.href = `/?search=${encodeURIComponent(searchQuery)}`;\n    }\n  };\n  const toggleSearch = () => {\n    setShowSearch(!showSearch);\n    if (!showSearch) {\n      setTimeout(() => {\n        var _document$querySelect;\n        (_document$querySelect = document.querySelector('.search-input')) === null || _document$querySelect === void 0 ? void 0 : _document$querySelect.focus();\n      }, 100);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `navbar ${darkMode ? 'dark-mode' : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-logo\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: logo,\n          alt: \"FashionClub\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"FashionClub\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      className: \"nav-dr\",\n      onClick: dropdownToggle,\n      src: nav_dropdown,\n      alt: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      ref: menuRef,\n      className: \"nav-menu\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setMenu(\"shop\"),\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          children: \"Shop\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 21\n        }, this), menu === \"shop\" && /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 41\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setMenu(\"Men\"),\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/mens\",\n          children: \"Men\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 21\n        }, this), menu === \"Men\" && /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 40\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setMenu(\"Women\"),\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/womens\",\n          children: \"Women\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 21\n        }, this), menu === \"Women\" && /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 42\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setMenu(\"Kids\"),\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/kids\",\n          children: \"Kids\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 21\n        }, this), menu === \"Kids\" && /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 41\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        onClick: () => setMenu(\"Portfolio\"),\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/portfolio\",\n          children: \"Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 21\n        }, this), menu === \"Portfolio\" && /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 46\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-actions\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `search-container ${showSearch ? 'active' : ''}`,\n        children: [showSearch && /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSearch,\n          className: \"search-form\",\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search products...\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            className: \"search-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"nav-icon-btn\",\n          onClick: toggleSearch,\n          title: \"Search\",\n          children: /*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"nav-icon-btn\",\n        onClick: toggleDarkMode,\n        title: \"Toggle Dark Mode\",\n        children: darkMode ? /*#__PURE__*/_jsxDEV(FaSun, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 33\n        }, this) : /*#__PURE__*/_jsxDEV(FaMoon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 17\n      }, this), localStorage.getItem('auth-token') && /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/wishlist\",\n        className: \"nav-icon-link\",\n        onClick: () => setMenu(\"wishlist\"),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-icon-container\",\n          children: [/*#__PURE__*/_jsxDEV(FaHeart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 29\n          }, this), wishlistCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"count-badge\",\n            children: wishlistCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 51\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-login-cart\",\n        children: [localStorage.getItem('auth-token') ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"nav-icon-btn user-btn\",\n            title: \"Account\",\n            children: /*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-dropdown\",\n            children: [/*#__PURE__*/_jsxDEV(Link, {\n              to: \"/profile\",\n              children: \"Profile\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Link, {\n              to: \"/orders\",\n              children: \"Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                localStorage.removeItem('auth-token');\n                window.location.replace('/');\n              },\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 25\n        }, this) : /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"login-btn\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/cart\",\n          className: \"nav-icon-link\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"nav-icon-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: cart_icon,\n              alt: \"Shopping Cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 29\n            }, this), getTotalCartItems() > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"count-badge\",\n              children: getTotalCartItems()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 9\n  }, this);\n};\n_s(Navbar, \"esN136RIXefpbOOURvssBBpSfoo=\", false, function () {\n  return [useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "useRef", "useEffect", "logo", "cart_icon", "Link", "useLocation", "ShopContext", "nav_dropdown", "FaHeart", "FaSearch", "FaMoon", "FaSun", "FaUser", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "menu", "setMenu", "darkMode", "setDarkMode", "showSearch", "setShowSearch", "searchQuery", "setSearch<PERSON>uery", "wishlistCount", "setWishlistCount", "getTotalCartItems", "menuRef", "location", "path", "pathname", "savedDarkMode", "localStorage", "getItem", "document", "body", "classList", "add", "fetchWishlistCount", "response", "fetch", "method", "headers", "Accept", "data", "json", "length", "error", "console", "dropdownToggle", "e", "current", "toggle", "target", "toggleDarkMode", "newDarkMode", "setItem", "remove", "handleSearch", "preventDefault", "trim", "window", "href", "encodeURIComponent", "toggleSearch", "setTimeout", "_document$querySelect", "querySelector", "focus", "className", "children", "to", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "onSubmit", "type", "placeholder", "value", "onChange", "title", "removeItem", "replace", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Navbar/Navbar.jsx"], "sourcesContent": ["import React, { useState, useContext, useRef, useEffect } from 'react';\r\nimport './Navbar.css';\r\nimport logo from '../Asset/logo1.png';\r\nimport cart_icon from '../Asset/cart_icon.png';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { ShopContext } from '../../Context/ShopContext';\r\nimport nav_dropdown from '../Asset/dropdown_icon.png';\r\nimport { FaHeart, FaSearch, FaMoon, FaSun, FaUser } from 'react-icons/fa';\r\n\r\nconst Navbar = () => {\r\n    const [menu, setMenu] = useState(\"shop\");\r\n    const [darkMode, setDarkMode] = useState(false);\r\n    const [showSearch, setShowSearch] = useState(false);\r\n    const [searchQuery, setSearchQuery] = useState('');\r\n    const [wishlistCount, setWishlistCount] = useState(0);\r\n    const { getTotalCartItems } = useContext(ShopContext);\r\n    const menuRef = useRef();\r\n    const location = useLocation();\r\n\r\n    useEffect(() => {\r\n        // Set active menu based on current path\r\n        const path = location.pathname;\r\n        if (path === '/') setMenu(\"shop\");\r\n        else if (path === '/mens') setMenu(\"Men\");\r\n        else if (path === '/womens') setMenu(\"Women\");\r\n        else if (path === '/kids') setMenu(\"Kids\");\r\n        else if (path === '/wishlist') setMenu(\"wishlist\");\r\n\r\n        // Load dark mode preference\r\n        const savedDarkMode = localStorage.getItem('darkMode') === 'true';\r\n        setDarkMode(savedDarkMode);\r\n\r\n        // Apply dark mode to body\r\n        if (savedDarkMode) {\r\n            document.body.classList.add('dark-mode');\r\n        }\r\n\r\n        // Fetch wishlist count\r\n        fetchWishlistCount();\r\n    }, [location]);\r\n\r\n    const fetchWishlistCount = async () => {\r\n        if (localStorage.getItem('auth-token')) {\r\n            try {\r\n                const response = await fetch('http://localhost:3000/getwishlist', {\r\n                    method: 'POST',\r\n                    headers: {\r\n                        Accept: 'application/json',\r\n                        'auth-token': `${localStorage.getItem('auth-token')}`,\r\n                        'Content-Type': 'application/json',\r\n                    },\r\n                    body: \"\",\r\n                });\r\n                const data = await response.json();\r\n                setWishlistCount(data.length || 0);\r\n            } catch (error) {\r\n                console.error('Error fetching wishlist:', error);\r\n            }\r\n        }\r\n    };\r\n\r\n    const dropdownToggle = (e) => {\r\n        menuRef.current.classList.toggle('nav-menu-visible');\r\n        e.target.classList.toggle('open');\r\n    }\r\n\r\n    const toggleDarkMode = () => {\r\n        const newDarkMode = !darkMode;\r\n        setDarkMode(newDarkMode);\r\n        localStorage.setItem('darkMode', newDarkMode);\r\n\r\n        if (newDarkMode) {\r\n            document.body.classList.add('dark-mode');\r\n        } else {\r\n            document.body.classList.remove('dark-mode');\r\n        }\r\n    }\r\n\r\n    const handleSearch = (e) => {\r\n        e.preventDefault();\r\n        if (searchQuery.trim()) {\r\n            // Navigate to search results page or filter current page\r\n            window.location.href = `/?search=${encodeURIComponent(searchQuery)}`;\r\n        }\r\n    };\r\n\r\n    const toggleSearch = () => {\r\n        setShowSearch(!showSearch);\r\n        if (!showSearch) {\r\n            setTimeout(() => {\r\n                document.querySelector('.search-input')?.focus();\r\n            }, 100);\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div className={`navbar ${darkMode ? 'dark-mode' : ''}`}>\r\n            <div className=\"nav-logo\">\r\n                <Link to=\"/\">\r\n                    <img src={logo} alt=\"FashionClub\" />\r\n                    <p>FashionClub</p>\r\n                </Link>\r\n            </div>\r\n\r\n            <img className='nav-dr' onClick={dropdownToggle} src={nav_dropdown} alt=\"\" />\r\n\r\n            <ul ref={menuRef} className=\"nav-menu\">\r\n                <li onClick={() => setMenu(\"shop\")}>\r\n                    <Link to='/'>Shop</Link>\r\n                    {menu === \"shop\" && <hr />}\r\n                </li>\r\n                <li onClick={() => setMenu(\"Men\")}>\r\n                    <Link to='/mens'>Men</Link>\r\n                    {menu === \"Men\" && <hr />}\r\n                </li>\r\n                <li onClick={() => setMenu(\"Women\")}>\r\n                    <Link to='/womens'>Women</Link>\r\n                    {menu === \"Women\" && <hr />}\r\n                </li>\r\n                <li onClick={() => setMenu(\"Kids\")}>\r\n                    <Link to='/kids'>Kids</Link>\r\n                    {menu === \"Kids\" && <hr />}\r\n                </li>\r\n                <li onClick={() => setMenu(\"Portfolio\")}>\r\n                    <Link to='/portfolio'>Portfolio</Link>\r\n                    {menu === \"Portfolio\" && <hr />}\r\n                </li>\r\n            </ul>\r\n\r\n            <div className=\"nav-actions\">\r\n                {/* Search */}\r\n                <div className={`search-container ${showSearch ? 'active' : ''}`}>\r\n                    {showSearch && (\r\n                        <form onSubmit={handleSearch} className=\"search-form\">\r\n                            <input\r\n                                type=\"text\"\r\n                                placeholder=\"Search products...\"\r\n                                value={searchQuery}\r\n                                onChange={(e) => setSearchQuery(e.target.value)}\r\n                                className=\"search-input\"\r\n                            />\r\n                        </form>\r\n                    )}\r\n                    <button className=\"nav-icon-btn\" onClick={toggleSearch} title=\"Search\">\r\n                        <FaSearch />\r\n                    </button>\r\n                </div>\r\n\r\n                {/* Dark Mode Toggle */}\r\n                <button className=\"nav-icon-btn\" onClick={toggleDarkMode} title=\"Toggle Dark Mode\">\r\n                    {darkMode ? <FaSun /> : <FaMoon />}\r\n                </button>\r\n\r\n                {/* Wishlist */}\r\n                {localStorage.getItem('auth-token') && (\r\n                    <Link to='/wishlist' className=\"nav-icon-link\" onClick={() => setMenu(\"wishlist\")}>\r\n                        <div className=\"nav-icon-container\">\r\n                            <FaHeart />\r\n                            {wishlistCount > 0 && <span className=\"count-badge\">{wishlistCount}</span>}\r\n                        </div>\r\n                    </Link>\r\n                )}\r\n\r\n                {/* User Account */}\r\n                <div className=\"nav-login-cart\">\r\n                    {localStorage.getItem('auth-token') ? (\r\n                        <div className=\"user-menu\">\r\n                            <button className=\"nav-icon-btn user-btn\" title=\"Account\">\r\n                                <FaUser />\r\n                            </button>\r\n                            <div className=\"user-dropdown\">\r\n                                <Link to=\"/profile\">Profile</Link>\r\n                                <Link to=\"/orders\">Orders</Link>\r\n                                <button onClick={() => {\r\n                                    localStorage.removeItem('auth-token');\r\n                                    window.location.replace('/')\r\n                                }}>\r\n                                    Logout\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    ) : (\r\n                        <Link to='/login'>\r\n                            <button className=\"login-btn\">Login</button>\r\n                        </Link>\r\n                    )}\r\n\r\n                    {/* Cart */}\r\n                    <Link to='/cart' className=\"nav-icon-link\">\r\n                        <div className=\"nav-icon-container\">\r\n                            <img src={cart_icon} alt=\"Shopping Cart\" />\r\n                            {getTotalCartItems() > 0 && (\r\n                                <span className=\"count-badge\">{getTotalCartItems()}</span>\r\n                            )}\r\n                        </div>\r\n                    </Link>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACtE,OAAO,cAAc;AACrB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,OAAOC,YAAY,MAAM,4BAA4B;AACrD,SAASC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGpB,QAAQ,CAAC,MAAM,CAAC;EACxC,MAAM,CAACqB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACuB,UAAU,EAAEC,aAAa,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM;IAAE6B;EAAkB,CAAC,GAAG5B,UAAU,CAACO,WAAW,CAAC;EACrD,MAAMsB,OAAO,GAAG5B,MAAM,CAAC,CAAC;EACxB,MAAM6B,QAAQ,GAAGxB,WAAW,CAAC,CAAC;EAE9BJ,SAAS,CAAC,MAAM;IACZ;IACA,MAAM6B,IAAI,GAAGD,QAAQ,CAACE,QAAQ;IAC9B,IAAID,IAAI,KAAK,GAAG,EAAEZ,OAAO,CAAC,MAAM,CAAC,CAAC,KAC7B,IAAIY,IAAI,KAAK,OAAO,EAAEZ,OAAO,CAAC,KAAK,CAAC,CAAC,KACrC,IAAIY,IAAI,KAAK,SAAS,EAAEZ,OAAO,CAAC,OAAO,CAAC,CAAC,KACzC,IAAIY,IAAI,KAAK,OAAO,EAAEZ,OAAO,CAAC,MAAM,CAAC,CAAC,KACtC,IAAIY,IAAI,KAAK,WAAW,EAAEZ,OAAO,CAAC,UAAU,CAAC;;IAElD;IACA,MAAMc,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,KAAK,MAAM;IACjEd,WAAW,CAACY,aAAa,CAAC;;IAE1B;IACA,IAAIA,aAAa,EAAE;MACfG,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;IAC5C;;IAEA;IACAC,kBAAkB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACV,QAAQ,CAAC,CAAC;EAEd,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIN,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACpC,IAAI;QACA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;UAC9DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACLC,MAAM,EAAE,kBAAkB;YAC1B,YAAY,EAAG,GAAEX,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,EAAC;YACrD,cAAc,EAAE;UACpB,CAAC;UACDE,IAAI,EAAE;QACV,CAAC,CAAC;QACF,MAAMS,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCpB,gBAAgB,CAACmB,IAAI,CAACE,MAAM,IAAI,CAAC,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACpD;IACJ;EACJ,CAAC;EAED,MAAME,cAAc,GAAIC,CAAC,IAAK;IAC1BvB,OAAO,CAACwB,OAAO,CAACf,SAAS,CAACgB,MAAM,CAAC,kBAAkB,CAAC;IACpDF,CAAC,CAACG,MAAM,CAACjB,SAAS,CAACgB,MAAM,CAAC,MAAM,CAAC;EACrC,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IACzB,MAAMC,WAAW,GAAG,CAACrC,QAAQ;IAC7BC,WAAW,CAACoC,WAAW,CAAC;IACxBvB,YAAY,CAACwB,OAAO,CAAC,UAAU,EAAED,WAAW,CAAC;IAE7C,IAAIA,WAAW,EAAE;MACbrB,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;IAC5C,CAAC,MAAM;MACHH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACqB,MAAM,CAAC,WAAW,CAAC;IAC/C;EACJ,CAAC;EAED,MAAMC,YAAY,GAAIR,CAAC,IAAK;IACxBA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClB,IAAIrC,WAAW,CAACsC,IAAI,CAAC,CAAC,EAAE;MACpB;MACAC,MAAM,CAACjC,QAAQ,CAACkC,IAAI,GAAI,YAAWC,kBAAkB,CAACzC,WAAW,CAAE,EAAC;IACxE;EACJ,CAAC;EAED,MAAM0C,YAAY,GAAGA,CAAA,KAAM;IACvB3C,aAAa,CAAC,CAACD,UAAU,CAAC;IAC1B,IAAI,CAACA,UAAU,EAAE;MACb6C,UAAU,CAAC,MAAM;QAAA,IAAAC,qBAAA;QACb,CAAAA,qBAAA,GAAAhC,QAAQ,CAACiC,aAAa,CAAC,eAAe,CAAC,cAAAD,qBAAA,uBAAvCA,qBAAA,CAAyCE,KAAK,CAAC,CAAC;MACpD,CAAC,EAAE,GAAG,CAAC;IACX;EACJ,CAAC;EAED,oBACIvD,OAAA;IAAKwD,SAAS,EAAG,UAASnD,QAAQ,GAAG,WAAW,GAAG,EAAG,EAAE;IAAAoD,QAAA,gBACpDzD,OAAA;MAAKwD,SAAS,EAAC,UAAU;MAAAC,QAAA,eACrBzD,OAAA,CAACV,IAAI;QAACoE,EAAE,EAAC,GAAG;QAAAD,QAAA,gBACRzD,OAAA;UAAK2D,GAAG,EAAEvE,IAAK;UAACwE,GAAG,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpChE,OAAA;UAAAyD,QAAA,EAAG;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENhE,OAAA;MAAKwD,SAAS,EAAC,QAAQ;MAACS,OAAO,EAAE7B,cAAe;MAACuB,GAAG,EAAElE,YAAa;MAACmE,GAAG,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE7EhE,OAAA;MAAIkE,GAAG,EAAEpD,OAAQ;MAAC0C,SAAS,EAAC,UAAU;MAAAC,QAAA,gBAClCzD,OAAA;QAAIiE,OAAO,EAAEA,CAAA,KAAM7D,OAAO,CAAC,MAAM,CAAE;QAAAqD,QAAA,gBAC/BzD,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,GAAG;UAAAD,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACvB7D,IAAI,KAAK,MAAM,iBAAIH,OAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACLhE,OAAA;QAAIiE,OAAO,EAAEA,CAAA,KAAM7D,OAAO,CAAC,KAAK,CAAE;QAAAqD,QAAA,gBAC9BzD,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,OAAO;UAAAD,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC1B7D,IAAI,KAAK,KAAK,iBAAIH,OAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACLhE,OAAA;QAAIiE,OAAO,EAAEA,CAAA,KAAM7D,OAAO,CAAC,OAAO,CAAE;QAAAqD,QAAA,gBAChCzD,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,SAAS;UAAAD,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC9B7D,IAAI,KAAK,OAAO,iBAAIH,OAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACLhE,OAAA;QAAIiE,OAAO,EAAEA,CAAA,KAAM7D,OAAO,CAAC,MAAM,CAAE;QAAAqD,QAAA,gBAC/BzD,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,OAAO;UAAAD,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC3B7D,IAAI,KAAK,MAAM,iBAAIH,OAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eACLhE,OAAA;QAAIiE,OAAO,EAAEA,CAAA,KAAM7D,OAAO,CAAC,WAAW,CAAE;QAAAqD,QAAA,gBACpCzD,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACrC7D,IAAI,KAAK,WAAW,iBAAIH,OAAA;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAELhE,OAAA;MAAKwD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAExBzD,OAAA;QAAKwD,SAAS,EAAG,oBAAmBjD,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAE;QAAAkD,QAAA,GAC5DlD,UAAU,iBACPP,OAAA;UAAMmE,QAAQ,EAAEtB,YAAa;UAACW,SAAS,EAAC,aAAa;UAAAC,QAAA,eACjDzD,OAAA;YACIoE,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,oBAAoB;YAChCC,KAAK,EAAE7D,WAAY;YACnB8D,QAAQ,EAAGlC,CAAC,IAAK3B,cAAc,CAAC2B,CAAC,CAACG,MAAM,CAAC8B,KAAK,CAAE;YAChDd,SAAS,EAAC;UAAc;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACT,eACDhE,OAAA;UAAQwD,SAAS,EAAC,cAAc;UAACS,OAAO,EAAEd,YAAa;UAACqB,KAAK,EAAC,QAAQ;UAAAf,QAAA,eAClEzD,OAAA,CAACL,QAAQ;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGNhE,OAAA;QAAQwD,SAAS,EAAC,cAAc;QAACS,OAAO,EAAExB,cAAe;QAAC+B,KAAK,EAAC,kBAAkB;QAAAf,QAAA,EAC7EpD,QAAQ,gBAAGL,OAAA,CAACH,KAAK;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGhE,OAAA,CAACJ,MAAM;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,EAGR7C,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,iBAC/BpB,OAAA,CAACV,IAAI;QAACoE,EAAE,EAAC,WAAW;QAACF,SAAS,EAAC,eAAe;QAACS,OAAO,EAAEA,CAAA,KAAM7D,OAAO,CAAC,UAAU,CAAE;QAAAqD,QAAA,eAC9EzD,OAAA;UAAKwD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAC/BzD,OAAA,CAACN,OAAO;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACVrD,aAAa,GAAG,CAAC,iBAAIX,OAAA;YAAMwD,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE9C;UAAa;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACT,eAGDhE,OAAA;QAAKwD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAC1BtC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,gBAC/BpB,OAAA;UAAKwD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtBzD,OAAA;YAAQwD,SAAS,EAAC,uBAAuB;YAACgB,KAAK,EAAC,SAAS;YAAAf,QAAA,eACrDzD,OAAA,CAACF,MAAM;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACThE,OAAA;YAAKwD,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BzD,OAAA,CAACV,IAAI;cAACoE,EAAE,EAAC,UAAU;cAAAD,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClChE,OAAA,CAACV,IAAI;cAACoE,EAAE,EAAC,SAAS;cAAAD,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChChE,OAAA;cAAQiE,OAAO,EAAEA,CAAA,KAAM;gBACnB9C,YAAY,CAACsD,UAAU,CAAC,YAAY,CAAC;gBACrCzB,MAAM,CAACjC,QAAQ,CAAC2D,OAAO,CAAC,GAAG,CAAC;cAChC,CAAE;cAAAjB,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,gBAENhE,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,QAAQ;UAAAD,QAAA,eACbzD,OAAA;YAAQwD,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CACT,eAGDhE,OAAA,CAACV,IAAI;UAACoE,EAAE,EAAC,OAAO;UAACF,SAAS,EAAC,eAAe;UAAAC,QAAA,eACtCzD,OAAA;YAAKwD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBAC/BzD,OAAA;cAAK2D,GAAG,EAAEtE,SAAU;cAACuE,GAAG,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1CnD,iBAAiB,CAAC,CAAC,GAAG,CAAC,iBACpBb,OAAA;cAAMwD,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAE5C,iBAAiB,CAAC;YAAC;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAC5D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC9D,EAAA,CA/LID,MAAM;EAAA,QAQSV,WAAW;AAAA;AAAAoF,EAAA,GAR1B1E,MAAM;AAiMZ,eAAeA,MAAM;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}