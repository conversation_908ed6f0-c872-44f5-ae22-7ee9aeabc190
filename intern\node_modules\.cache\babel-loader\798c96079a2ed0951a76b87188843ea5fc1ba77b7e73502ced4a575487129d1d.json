{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Footer\\\\Footer.jsx\";\nimport React from 'react';\nimport './Footer.css';\nimport footer_logo from '../Asset/logo_big.png';\nimport instagram_icon from '../Asset/instagram_icon.png';\nimport pinterest_icon from '../Asset/pintester_icon.png';\nimport whatsapp_icon from '../Asset/whatsapp_icon.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"footer\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-logo\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: footer_logo,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"FashionClub\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"footer-links\",\n      children: [/*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Company\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Offices\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"About\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n        children: \"Contact\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-social-icons\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-icon-container\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: instagram_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-icon-container\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: pinterest_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-icon-container\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: whatsapp_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"footer-copyright\",\n      children: [/*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Copyright @ 2024 Right Reserve\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "footer_logo", "instagram_icon", "pinterest_icon", "whatsapp_icon", "jsxDEV", "_jsxDEV", "Footer", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Footer/Footer.jsx"], "sourcesContent": ["import React from 'react';\r\nimport './Footer.css';\r\nimport footer_logo from '../Asset/logo_big.png';\r\nimport instagram_icon from '../Asset/instagram_icon.png';\r\nimport pinterest_icon from '../Asset/pintester_icon.png';\r\nimport whatsapp_icon from '../Asset/whatsapp_icon.png';\r\n\r\nconst Footer = () => {\r\n    return (\r\n        <div className=\"footer\">\r\n            <div className=\"footer-logo\">\r\n                <img src={footer_logo} alt=\"\" />\r\n                <p>FashionClub</p>\r\n            </div>\r\n            <ul className=\"footer-links\">\r\n                <li>Company</li>\r\n                <li>Products</li>\r\n                <li>Offices</li>\r\n                <li>About</li>\r\n                <li>Contact</li>\r\n            </ul>\r\n            <div className=\"footer-social-icons\">\r\n                <div className=\"footer-icon-container\">\r\n                    <img src={instagram_icon} alt=\"\" />\r\n                </div>\r\n                <div className=\"footer-icon-container\">\r\n                    <img src={pinterest_icon} alt=\"\" />\r\n                </div>\r\n                <div className=\"footer-icon-container\">\r\n                    <img src={whatsapp_icon} alt=\"\" />\r\n                </div>\r\n            </div>\r\n            <div className=\"footer-copyright\">\r\n                <hr />\r\n                <p>Copyright @ 2024 Right Reserve</p>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Footer;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AACrB,OAAOC,WAAW,MAAM,uBAAuB;AAC/C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,MAAM,GAAGA,CAAA,KAAM;EACjB,oBACID,OAAA;IAAKE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACnBH,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBH,OAAA;QAAKI,GAAG,EAAET,WAAY;QAACU,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAChCT,OAAA;QAAAG,QAAA,EAAG;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,eACNT,OAAA;MAAIE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBACxBH,OAAA;QAAAG,QAAA,EAAI;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChBT,OAAA;QAAAG,QAAA,EAAI;MAAQ;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBT,OAAA;QAAAG,QAAA,EAAI;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChBT,OAAA;QAAAG,QAAA,EAAI;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdT,OAAA;QAAAG,QAAA,EAAI;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eACLT,OAAA;MAAKE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChCH,OAAA;QAAKE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eAClCH,OAAA;UAAKI,GAAG,EAAER,cAAe;UAACS,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNT,OAAA;QAAKE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eAClCH,OAAA;UAAKI,GAAG,EAAEP,cAAe;UAACQ,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNT,OAAA;QAAKE,SAAS,EAAC,uBAAuB;QAAAC,QAAA,eAClCH,OAAA;UAAKI,GAAG,EAAEN,aAAc;UAACO,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNT,OAAA;MAAKE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC7BH,OAAA;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNT,OAAA;QAAAG,QAAA,EAAG;MAA8B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACC,EAAA,GA/BIT,MAAM;AAiCZ,eAAeA,MAAM;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}