{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Pages\\\\Product.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport { ShopContext } from '../Context/ShopContext';\nimport { useParams } from 'react-router-dom';\nimport Breadcrums from '../Components/Breadcrums/Breadcrums';\nimport ProductDisplay from '../Components/ProductDisplay/ProductDisplay';\nimport DescriptionBox from '../Components/DescriptionBox/DescriptionBox';\nimport RelatedProducts from '../Components/RelatedProducts/RelatedProducts';\n// import CartItems from '../Components/CartItems/CartItems'\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Product = () => {\n  _s();\n  const {\n    all_product\n  } = useContext(ShopContext);\n  const {\n    productId\n  } = useParams();\n  const product = all_product.find(e => e.id === Number(productId));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(Breadcrums, {\n      product: product\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ProductDisplay, {\n      product: product\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(DescriptionBox, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(RelatedProducts, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 9\n  }, this);\n};\n_s(Product, \"+KS2qRJ93JbPgoOMNJlO1rOGwrQ=\", false, function () {\n  return [useParams];\n});\n_c = Product;\nexport default Product;\nvar _c;\n$RefreshReg$(_c, \"Product\");", "map": {"version": 3, "names": ["React", "useContext", "ShopContext", "useParams", "Breadcrums", "ProductDisplay", "DescriptionBox", "RelatedProducts", "jsxDEV", "_jsxDEV", "Product", "_s", "all_product", "productId", "product", "find", "e", "id", "Number", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Pages/Product.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\r\nimport { ShopContext } from '../Context/ShopContext';\r\nimport { useParams } from 'react-router-dom';\r\nimport Breadcrums from '../Components/Breadcrums/Breadcrums';\r\nimport ProductDisplay from '../Components/ProductDisplay/ProductDisplay';\r\nimport DescriptionBox from '../Components/DescriptionBox/DescriptionBox';\r\nimport RelatedProducts from '../Components/RelatedProducts/RelatedProducts';\r\n// import CartItems from '../Components/CartItems/CartItems'\r\n\r\nconst Product = () => {\r\n    const { all_product } = useContext(ShopContext);\r\n    const { productId } = useParams();\r\n    const product = all_product.find((e) => e.id === Number(productId));\r\n\r\n    return (\r\n        <div>\r\n            <Breadcrums product={product} />\r\n            <ProductDisplay product={product} />\r\n            <DescriptionBox />\r\n            <RelatedProducts />\r\n            {/* <CartItems /> Include CartItems here */}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Product;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,SAASC,WAAW,QAAQ,wBAAwB;AACpD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,UAAU,MAAM,qCAAqC;AAC5D,OAAOC,cAAc,MAAM,6CAA6C;AACxE,OAAOC,cAAc,MAAM,6CAA6C;AACxE,OAAOC,eAAe,MAAM,+CAA+C;AAC3E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAY,CAAC,GAAGX,UAAU,CAACC,WAAW,CAAC;EAC/C,MAAM;IAAEW;EAAU,CAAC,GAAGV,SAAS,CAAC,CAAC;EACjC,MAAMW,OAAO,GAAGF,WAAW,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,KAAKC,MAAM,CAACL,SAAS,CAAC,CAAC;EAEnE,oBACIJ,OAAA;IAAAU,QAAA,gBACIV,OAAA,CAACL,UAAU;MAACU,OAAO,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChCd,OAAA,CAACJ,cAAc;MAACS,OAAO,EAAEA;IAAQ;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpCd,OAAA,CAACH,cAAc;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBd,OAAA,CAACF,eAAe;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAElB,CAAC;AAEd,CAAC;AAAAZ,EAAA,CAdKD,OAAO;EAAA,QAEaP,SAAS;AAAA;AAAAqB,EAAA,GAF7Bd,OAAO;AAgBb,eAAeA,OAAO;AAAC,IAAAc,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}