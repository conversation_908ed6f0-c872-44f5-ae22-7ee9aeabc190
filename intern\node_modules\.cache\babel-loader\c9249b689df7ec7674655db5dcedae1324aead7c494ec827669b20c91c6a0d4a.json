{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\NewsLetter\\\\NewsLetter.jsx\";\nimport React from 'react';\nimport './NewsLetter.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewsLetter = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"newsLetter\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Get Exclusive Ofeers On Your Email\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Subscribe to Our NewLetter and stay updated\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"email\",\n        placeholder: \"Your Email id\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        children: \"Subscribe\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = NewsLetter;\nexport default NewsLetter;\nvar _c;\n$RefreshReg$(_c, \"NewsLetter\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "NewsLetter", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/NewsLetter/NewsLetter.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './NewsLetter.css'\r\n\r\nconst NewsLetter = () => {\r\n    return (\r\n        <div className=\"newsLetter\">\r\n            <h1>Get Exclusive Ofeers On Your Email</h1>\r\n            <p>Subscribe to Our NewLetter and stay updated</p>\r\n            <div>\r\n                <input type=\"email\" placeholder=\"Your Email id\" />\r\n                <button>Subscribe</button>\r\n            </div>\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default NewsLetter\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACrB,oBACID,OAAA;IAAKE,SAAS,EAAC,YAAY;IAAAC,QAAA,gBACvBH,OAAA;MAAAG,QAAA,EAAI;IAAkC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3CP,OAAA;MAAAG,QAAA,EAAG;IAA2C;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAClDP,OAAA;MAAAG,QAAA,gBACIH,OAAA;QAAOQ,IAAI,EAAC,OAAO;QAACC,WAAW,EAAC;MAAe;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDP,OAAA;QAAAG,QAAA,EAAQ;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEL,CAAC;AAEd,CAAC;AAAAG,EAAA,GAZKT,UAAU;AAchB,eAAeA,UAAU;AAAA,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}