{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\UserProfile\\\\UserProfile.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { FaUser, FaEdit, FaSave, FaTimes, FaShoppingBag, FaHeart, FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa';\nimport './UserProfile.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserProfile = () => {\n  _s();\n  const [user, setUser] = useState({\n    name: '',\n    email: '',\n    phone: '',\n    address: '',\n    city: '',\n    state: '',\n    zipCode: '',\n    country: ''\n  });\n  const [isEditing, setIsEditing] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [orders, setOrders] = useState([]);\n  const [wishlistCount, setWishlistCount] = useState(0);\n  useEffect(() => {\n    fetchUserProfile();\n    fetchUserOrders();\n    fetchWishlistCount();\n  }, []);\n  const fetchUserProfile = async () => {\n    // For now, we'll use mock data since we don't have user profile endpoints\n    const mockUser = {\n      name: 'John Doe',\n      email: '<EMAIL>',\n      phone: '+****************',\n      address: '123 Fashion Street',\n      city: 'New York',\n      state: 'NY',\n      zipCode: '10001',\n      country: 'United States'\n    };\n    setUser(mockUser);\n  };\n  const fetchUserOrders = async () => {\n    // Mock orders data\n    const mockOrders = [{\n      id: 'ORD-001',\n      date: '2024-01-15',\n      total: 2499,\n      status: 'Delivered',\n      items: 3\n    }, {\n      id: 'ORD-002',\n      date: '2024-01-10',\n      total: 1899,\n      status: 'Shipped',\n      items: 2\n    }, {\n      id: 'ORD-003',\n      date: '2024-01-05',\n      total: 3299,\n      status: 'Processing',\n      items: 4\n    }];\n    setOrders(mockOrders);\n  };\n  const fetchWishlistCount = async () => {\n    if (localStorage.getItem('auth-token')) {\n      try {\n        const response = await fetch('http://localhost:3000/getwishlist', {\n          method: 'POST',\n          headers: {\n            Accept: 'application/json',\n            'auth-token': `${localStorage.getItem('auth-token')}`,\n            'Content-Type': 'application/json'\n          },\n          body: \"\"\n        });\n        const data = await response.json();\n        setWishlistCount(data.length || 0);\n      } catch (error) {\n        console.error('Error fetching wishlist:', error);\n        setWishlistCount(5); // Mock data\n      }\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSaveProfile = async () => {\n    setLoading(true);\n    try {\n      // Here you would normally send the updated profile to the backend\n      // For now, we'll just simulate a save\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setIsEditing(false);\n      alert('Profile updated successfully!');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n      alert('Failed to update profile');\n    }\n    setLoading(false);\n  };\n  const handleCancelEdit = () => {\n    setIsEditing(false);\n    fetchUserProfile(); // Reset to original data\n  };\n  const getStatusColor = status => {\n    switch (status.toLowerCase()) {\n      case 'delivered':\n        return '#28a745';\n      case 'shipped':\n        return '#007bff';\n      case 'processing':\n        return '#ffc107';\n      case 'cancelled':\n        return '#dc3545';\n      default:\n        return '#6c757d';\n    }\n  };\n  if (!localStorage.getItem('auth-token')) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"not-logged-in\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Please log in to view your profile\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.href = '/login',\n          className: \"login-btn\",\n          children: \"Go to Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"profile-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-avatar\",\n        children: /*#__PURE__*/_jsxDEV(FaUser, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: [\"Welcome back, \", user.name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Manage your account and track your orders\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"profile-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-stats\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(FaShoppingBag, {\n            className: \"stat-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: orders.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Total Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(FaHeart, {\n            className: \"stat-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: wishlistCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Wishlist Items\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-card\",\n          children: [/*#__PURE__*/_jsxDEV(FaMapMarkerAlt, {\n            className: \"stat-icon\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: user.city\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Location\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"profile-sections\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"profile-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"section-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"Profile Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 29\n            }, this), !isEditing ? /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setIsEditing(true),\n              className: \"edit-btn\",\n              children: [/*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 37\n              }, this), \" Edit\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"edit-actions\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleSaveProfile,\n                disabled: loading,\n                className: \"save-btn\",\n                children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 41\n                }, this), \" \", loading ? 'Saving...' : 'Save']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: handleCancelEdit,\n                className: \"cancel-btn\",\n                children: [/*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 41\n                }, this), \" Cancel\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"profile-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Full Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: user.name,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Email\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: user.email,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Phone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"tel\",\n                  name: \"phone\",\n                  value: user.phone,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"address\",\n                  value: user.address,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"City\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"city\",\n                  value: user.city,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"State\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"state\",\n                  value: user.state,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Zip Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"zipCode\",\n                  value: user.zipCode,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  children: \"Country\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"country\",\n                  value: user.country,\n                  onChange: handleInputChange,\n                  disabled: !isEditing\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"recent-orders\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Recent Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"orders-list\",\n            children: orders.map(order => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"order-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  children: [\"Order #\", order.id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: new Date(order.date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: [order.items, \" items\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"order-total\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"amount\",\n                  children: [\"\\u20B9\", order.total]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 313,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status\",\n                  style: {\n                    color: getStatusColor(order.status)\n                  },\n                  children: order.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 37\n              }, this)]\n            }, order.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"view-all-orders-btn\",\n            children: \"View All Orders\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 151,\n    columnNumber: 9\n  }, this);\n};\n_s(UserProfile, \"2SE/Ump5vwgYhdFIcK2uqFudmtw=\");\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "FaUser", "FaEdit", "FaSave", "FaTimes", "FaShoppingBag", "FaHeart", "FaMapMarkerAlt", "FaPhone", "FaEnvelope", "jsxDEV", "_jsxDEV", "UserProfile", "_s", "user", "setUser", "name", "email", "phone", "address", "city", "state", "zipCode", "country", "isEditing", "setIsEditing", "loading", "setLoading", "orders", "setOrders", "wishlistCount", "setWishlistCount", "fetchUserProfile", "fetchUserOrders", "fetchWishlistCount", "mockUser", "mockOrders", "id", "date", "total", "status", "items", "localStorage", "getItem", "response", "fetch", "method", "headers", "Accept", "body", "data", "json", "length", "error", "console", "handleInputChange", "e", "value", "target", "prev", "handleSaveProfile", "Promise", "resolve", "setTimeout", "alert", "handleCancelEdit", "getStatusColor", "toLowerCase", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "href", "disabled", "type", "onChange", "map", "order", "Date", "toLocaleDateString", "style", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/UserProfile/UserProfile.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { FaUser, FaEdit, FaSave, FaTimes, FaShoppingBag, FaHeart, FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa';\nimport './UserProfile.css';\n\nconst UserProfile = () => {\n    const [user, setUser] = useState({\n        name: '',\n        email: '',\n        phone: '',\n        address: '',\n        city: '',\n        state: '',\n        zipCode: '',\n        country: ''\n    });\n    const [isEditing, setIsEditing] = useState(false);\n    const [loading, setLoading] = useState(false);\n    const [orders, setOrders] = useState([]);\n    const [wishlistCount, setWishlistCount] = useState(0);\n\n    useEffect(() => {\n        fetchUserProfile();\n        fetchUserOrders();\n        fetchWishlistCount();\n    }, []);\n\n    const fetchUserProfile = async () => {\n        // For now, we'll use mock data since we don't have user profile endpoints\n        const mockUser = {\n            name: '<PERSON>',\n            email: '<EMAIL>',\n            phone: '+****************',\n            address: '123 Fashion Street',\n            city: 'New York',\n            state: 'NY',\n            zipCode: '10001',\n            country: 'United States'\n        };\n        setUser(mockUser);\n    };\n\n    const fetchUserOrders = async () => {\n        // Mock orders data\n        const mockOrders = [\n            {\n                id: 'ORD-001',\n                date: '2024-01-15',\n                total: 2499,\n                status: 'Delivered',\n                items: 3\n            },\n            {\n                id: 'ORD-002',\n                date: '2024-01-10',\n                total: 1899,\n                status: 'Shipped',\n                items: 2\n            },\n            {\n                id: 'ORD-003',\n                date: '2024-01-05',\n                total: 3299,\n                status: 'Processing',\n                items: 4\n            }\n        ];\n        setOrders(mockOrders);\n    };\n\n    const fetchWishlistCount = async () => {\n        if (localStorage.getItem('auth-token')) {\n            try {\n                const response = await fetch('http://localhost:3000/getwishlist', {\n                    method: 'POST',\n                    headers: {\n                        Accept: 'application/json',\n                        'auth-token': `${localStorage.getItem('auth-token')}`,\n                        'Content-Type': 'application/json',\n                    },\n                    body: \"\",\n                });\n                const data = await response.json();\n                setWishlistCount(data.length || 0);\n            } catch (error) {\n                console.error('Error fetching wishlist:', error);\n                setWishlistCount(5); // Mock data\n            }\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setUser(prev => ({\n            ...prev,\n            [name]: value\n        }));\n    };\n\n    const handleSaveProfile = async () => {\n        setLoading(true);\n        \n        try {\n            // Here you would normally send the updated profile to the backend\n            // For now, we'll just simulate a save\n            await new Promise(resolve => setTimeout(resolve, 1000));\n            \n            setIsEditing(false);\n            alert('Profile updated successfully!');\n        } catch (error) {\n            console.error('Error updating profile:', error);\n            alert('Failed to update profile');\n        }\n        \n        setLoading(false);\n    };\n\n    const handleCancelEdit = () => {\n        setIsEditing(false);\n        fetchUserProfile(); // Reset to original data\n    };\n\n    const getStatusColor = (status) => {\n        switch (status.toLowerCase()) {\n            case 'delivered':\n                return '#28a745';\n            case 'shipped':\n                return '#007bff';\n            case 'processing':\n                return '#ffc107';\n            case 'cancelled':\n                return '#dc3545';\n            default:\n                return '#6c757d';\n        }\n    };\n\n    if (!localStorage.getItem('auth-token')) {\n        return (\n            <div className=\"profile-container\">\n                <div className=\"not-logged-in\">\n                    <h2>Please log in to view your profile</h2>\n                    <button onClick={() => window.location.href = '/login'} className=\"login-btn\">\n                        Go to Login\n                    </button>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"profile-container\">\n            <div className=\"profile-header\">\n                <div className=\"profile-avatar\">\n                    <FaUser />\n                </div>\n                <div className=\"profile-info\">\n                    <h1>Welcome back, {user.name}!</h1>\n                    <p>Manage your account and track your orders</p>\n                </div>\n            </div>\n\n            <div className=\"profile-content\">\n                <div className=\"profile-stats\">\n                    <div className=\"stat-card\">\n                        <FaShoppingBag className=\"stat-icon\" />\n                        <div className=\"stat-info\">\n                            <h3>{orders.length}</h3>\n                            <p>Total Orders</p>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <FaHeart className=\"stat-icon\" />\n                        <div className=\"stat-info\">\n                            <h3>{wishlistCount}</h3>\n                            <p>Wishlist Items</p>\n                        </div>\n                    </div>\n                    <div className=\"stat-card\">\n                        <FaMapMarkerAlt className=\"stat-icon\" />\n                        <div className=\"stat-info\">\n                            <h3>{user.city}</h3>\n                            <p>Location</p>\n                        </div>\n                    </div>\n                </div>\n\n                <div className=\"profile-sections\">\n                    <div className=\"profile-details\">\n                        <div className=\"section-header\">\n                            <h2>Profile Information</h2>\n                            {!isEditing ? (\n                                <button onClick={() => setIsEditing(true)} className=\"edit-btn\">\n                                    <FaEdit /> Edit\n                                </button>\n                            ) : (\n                                <div className=\"edit-actions\">\n                                    <button onClick={handleSaveProfile} disabled={loading} className=\"save-btn\">\n                                        <FaSave /> {loading ? 'Saving...' : 'Save'}\n                                    </button>\n                                    <button onClick={handleCancelEdit} className=\"cancel-btn\">\n                                        <FaTimes /> Cancel\n                                    </button>\n                                </div>\n                            )}\n                        </div>\n\n                        <div className=\"profile-form\">\n                            <div className=\"form-row\">\n                                <div className=\"form-group\">\n                                    <label>Full Name</label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"name\"\n                                        value={user.name}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                                <div className=\"form-group\">\n                                    <label>Email</label>\n                                    <input\n                                        type=\"email\"\n                                        name=\"email\"\n                                        value={user.email}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                            </div>\n\n                            <div className=\"form-row\">\n                                <div className=\"form-group\">\n                                    <label>Phone</label>\n                                    <input\n                                        type=\"tel\"\n                                        name=\"phone\"\n                                        value={user.phone}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                                <div className=\"form-group\">\n                                    <label>Address</label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"address\"\n                                        value={user.address}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                            </div>\n\n                            <div className=\"form-row\">\n                                <div className=\"form-group\">\n                                    <label>City</label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"city\"\n                                        value={user.city}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                                <div className=\"form-group\">\n                                    <label>State</label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"state\"\n                                        value={user.state}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                            </div>\n\n                            <div className=\"form-row\">\n                                <div className=\"form-group\">\n                                    <label>Zip Code</label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"zipCode\"\n                                        value={user.zipCode}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                                <div className=\"form-group\">\n                                    <label>Country</label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"country\"\n                                        value={user.country}\n                                        onChange={handleInputChange}\n                                        disabled={!isEditing}\n                                    />\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"recent-orders\">\n                        <h2>Recent Orders</h2>\n                        <div className=\"orders-list\">\n                            {orders.map((order) => (\n                                <div key={order.id} className=\"order-item\">\n                                    <div className=\"order-info\">\n                                        <h3>Order #{order.id}</h3>\n                                        <p>{new Date(order.date).toLocaleDateString()}</p>\n                                        <p>{order.items} items</p>\n                                    </div>\n                                    <div className=\"order-total\">\n                                        <span className=\"amount\">₹{order.total}</span>\n                                        <span \n                                            className=\"status\" \n                                            style={{ color: getStatusColor(order.status) }}\n                                        >\n                                            {order.status}\n                                        </span>\n                                    </div>\n                                </div>\n                            ))}\n                        </div>\n                        <button className=\"view-all-orders-btn\">\n                            View All Orders\n                        </button>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,EAAEC,cAAc,EAAEC,OAAO,EAAEC,UAAU,QAAQ,gBAAgB;AAC7H,OAAO,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAC;IAC7BiB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,MAAM,EAAEC,SAAS,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EAErDC,SAAS,CAAC,MAAM;IACZgC,gBAAgB,CAAC,CAAC;IAClBC,eAAe,CAAC,CAAC;IACjBC,kBAAkB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMF,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACjC;IACA,MAAMG,QAAQ,GAAG;MACbnB,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,oBAAoB;MAC7BC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE;IACb,CAAC;IACDR,OAAO,CAACoB,QAAQ,CAAC;EACrB,CAAC;EAED,MAAMF,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC;IACA,MAAMG,UAAU,GAAG,CACf;MACIC,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,WAAW;MACnBC,KAAK,EAAE;IACX,CAAC,EACD;MACIJ,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,SAAS;MACjBC,KAAK,EAAE;IACX,CAAC,EACD;MACIJ,EAAE,EAAE,SAAS;MACbC,IAAI,EAAE,YAAY;MAClBC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,YAAY;MACpBC,KAAK,EAAE;IACX,CAAC,CACJ;IACDZ,SAAS,CAACO,UAAU,CAAC;EACzB,CAAC;EAED,MAAMF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAIQ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACpC,IAAI;QACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAE;UAC9DC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACLC,MAAM,EAAE,kBAAkB;YAC1B,YAAY,EAAG,GAAEN,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,EAAC;YACrD,cAAc,EAAE;UACpB,CAAC;UACDM,IAAI,EAAE;QACV,CAAC,CAAC;QACF,MAAMC,IAAI,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;QAClCpB,gBAAgB,CAACmB,IAAI,CAACE,MAAM,IAAI,CAAC,CAAC;MACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDtB,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;MACzB;IACJ;EACJ,CAAC;EAED,MAAMwB,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAExC,IAAI;MAAEyC;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAChC3C,OAAO,CAAC4C,IAAI,KAAK;MACb,GAAGA,IAAI;MACP,CAAC3C,IAAI,GAAGyC;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClCjC,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA;MACA;MACA,MAAM,IAAIkC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDrC,YAAY,CAAC,KAAK,CAAC;MACnBuC,KAAK,CAAC,+BAA+B,CAAC;IAC1C,CAAC,CAAC,OAAOX,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CW,KAAK,CAAC,0BAA0B,CAAC;IACrC;IAEArC,UAAU,CAAC,KAAK,CAAC;EACrB,CAAC;EAED,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC3BxC,YAAY,CAAC,KAAK,CAAC;IACnBO,gBAAgB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;EAED,MAAMkC,cAAc,GAAI1B,MAAM,IAAK;IAC/B,QAAQA,MAAM,CAAC2B,WAAW,CAAC,CAAC;MACxB,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB,KAAK,SAAS;QACV,OAAO,SAAS;MACpB,KAAK,YAAY;QACb,OAAO,SAAS;MACpB,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB;QACI,OAAO,SAAS;IACxB;EACJ,CAAC;EAED,IAAI,CAACzB,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;IACrC,oBACIhC,OAAA;MAAKyD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAC9B1D,OAAA;QAAKyD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B1D,OAAA;UAAA0D,QAAA,EAAI;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3C9D,OAAA;UAAQ+D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAS;UAACT,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAE9E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACI9D,OAAA;IAAKyD,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAC9B1D,OAAA;MAAKyD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3B1D,OAAA;QAAKyD,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC3B1D,OAAA,CAACV,MAAM;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACN9D,OAAA;QAAKyD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzB1D,OAAA;UAAA0D,QAAA,GAAI,gBAAc,EAACvD,IAAI,CAACE,IAAI,EAAC,GAAC;QAAA;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnC9D,OAAA;UAAA0D,QAAA,EAAG;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN9D,OAAA;MAAKyD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B1D,OAAA;QAAKyD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC1B1D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1D,OAAA,CAACN,aAAa;YAAC+D,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvC9D,OAAA;YAAKyD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB1D,OAAA;cAAA0D,QAAA,EAAKzC,MAAM,CAACwB;YAAM;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB9D,OAAA;cAAA0D,QAAA,EAAG;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1D,OAAA,CAACL,OAAO;YAAC8D,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjC9D,OAAA;YAAKyD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB1D,OAAA;cAAA0D,QAAA,EAAKvC;YAAa;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxB9D,OAAA;cAAA0D,QAAA,EAAG;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9D,OAAA;UAAKyD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACtB1D,OAAA,CAACJ,cAAc;YAAC6D,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxC9D,OAAA;YAAKyD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtB1D,OAAA;cAAA0D,QAAA,EAAKvD,IAAI,CAACM;YAAI;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpB9D,OAAA;cAAA0D,QAAA,EAAG;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN9D,OAAA;QAAKyD,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC7B1D,OAAA;UAAKyD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5B1D,OAAA;YAAKyD,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3B1D,OAAA;cAAA0D,QAAA,EAAI;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EAC3B,CAACjD,SAAS,gBACPb,OAAA;cAAQ+D,OAAO,EAAEA,CAAA,KAAMjD,YAAY,CAAC,IAAI,CAAE;cAAC2C,SAAS,EAAC,UAAU;cAAAC,QAAA,gBAC3D1D,OAAA,CAACT,MAAM;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,SACd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAET9D,OAAA;cAAKyD,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzB1D,OAAA;gBAAQ+D,OAAO,EAAEd,iBAAkB;gBAACkB,QAAQ,EAAEpD,OAAQ;gBAAC0C,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvE1D,OAAA,CAACR,MAAM;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,KAAC,EAAC/C,OAAO,GAAG,WAAW,GAAG,MAAM;cAAA;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,eACT9D,OAAA;gBAAQ+D,OAAO,EAAET,gBAAiB;gBAACG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACrD1D,OAAA,CAACP,OAAO;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,WACf;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEN9D,OAAA;YAAKyD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBACzB1D,OAAA;cAAKyD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB1D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxB9D,OAAA;kBACIoE,IAAI,EAAC,MAAM;kBACX/D,IAAI,EAAC,MAAM;kBACXyC,KAAK,EAAE3C,IAAI,CAACE,IAAK;kBACjBgE,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpB9D,OAAA;kBACIoE,IAAI,EAAC,OAAO;kBACZ/D,IAAI,EAAC,OAAO;kBACZyC,KAAK,EAAE3C,IAAI,CAACG,KAAM;kBAClB+D,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9D,OAAA;cAAKyD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB1D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpB9D,OAAA;kBACIoE,IAAI,EAAC,KAAK;kBACV/D,IAAI,EAAC,OAAO;kBACZyC,KAAK,EAAE3C,IAAI,CAACI,KAAM;kBAClB8D,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtB9D,OAAA;kBACIoE,IAAI,EAAC,MAAM;kBACX/D,IAAI,EAAC,SAAS;kBACdyC,KAAK,EAAE3C,IAAI,CAACK,OAAQ;kBACpB6D,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9D,OAAA;cAAKyD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB1D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnB9D,OAAA;kBACIoE,IAAI,EAAC,MAAM;kBACX/D,IAAI,EAAC,MAAM;kBACXyC,KAAK,EAAE3C,IAAI,CAACM,IAAK;kBACjB4D,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpB9D,OAAA;kBACIoE,IAAI,EAAC,MAAM;kBACX/D,IAAI,EAAC,OAAO;kBACZyC,KAAK,EAAE3C,IAAI,CAACO,KAAM;kBAClB2D,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEN9D,OAAA;cAAKyD,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACrB1D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACvB9D,OAAA;kBACIoE,IAAI,EAAC,MAAM;kBACX/D,IAAI,EAAC,SAAS;kBACdyC,KAAK,EAAE3C,IAAI,CAACQ,OAAQ;kBACpB0D,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,EAAO;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtB9D,OAAA;kBACIoE,IAAI,EAAC,MAAM;kBACX/D,IAAI,EAAC,SAAS;kBACdyC,KAAK,EAAE3C,IAAI,CAACS,OAAQ;kBACpByD,QAAQ,EAAEzB,iBAAkB;kBAC5BuB,QAAQ,EAAE,CAACtD;gBAAU;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN9D,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B1D,OAAA;YAAA0D,QAAA,EAAI;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtB9D,OAAA;YAAKyD,SAAS,EAAC,aAAa;YAAAC,QAAA,EACvBzC,MAAM,CAACqD,GAAG,CAAEC,KAAK,iBACdvE,OAAA;cAAoByD,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACtC1D,OAAA;gBAAKyD,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvB1D,OAAA;kBAAA0D,QAAA,GAAI,SAAO,EAACa,KAAK,CAAC7C,EAAE;gBAAA;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1B9D,OAAA;kBAAA0D,QAAA,EAAI,IAAIc,IAAI,CAACD,KAAK,CAAC5C,IAAI,CAAC,CAAC8C,kBAAkB,CAAC;gBAAC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClD9D,OAAA;kBAAA0D,QAAA,GAAIa,KAAK,CAACzC,KAAK,EAAC,QAAM;gBAAA;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC,eACN9D,OAAA;gBAAKyD,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBACxB1D,OAAA;kBAAMyD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,GAAC,QAAC,EAACa,KAAK,CAAC3C,KAAK;gBAAA;kBAAA+B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9C9D,OAAA;kBACIyD,SAAS,EAAC,QAAQ;kBAClBiB,KAAK,EAAE;oBAAEC,KAAK,EAAEpB,cAAc,CAACgB,KAAK,CAAC1C,MAAM;kBAAE,CAAE;kBAAA6B,QAAA,EAE9Ca,KAAK,CAAC1C;gBAAM;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA,GAdAS,KAAK,CAAC7C,EAAE;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAeb,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9D,OAAA;YAAQyD,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC5D,EAAA,CAvUID,WAAW;AAAA2E,EAAA,GAAX3E,WAAW;AAyUjB,eAAeA,WAAW;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}