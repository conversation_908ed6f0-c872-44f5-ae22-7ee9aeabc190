{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\ProductDisplay\\\\ProductDisplay.jsx\",\n  _s = $RefreshSig$();\nimport React, { useContext } from 'react';\nimport './ProductDisplay.css';\nimport star_icon from \"../Asset/star_icon.png\";\nimport star_dull_icon from \"../Asset/star_dull_icon.png\";\nimport { ShopContext } from '../../Context/ShopContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDisplay = props => {\n  _s();\n  const {\n    product\n  } = props;\n  const {\n    addToCart\n  } = useContext(ShopContext);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"productdisplay\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"productdisplay-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"productdisplay-img-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.image,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.image,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.image,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: product.image,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"productdisplay-img\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          className: \"productdisplay-main-img\",\n          src: product === null || product === void 0 ? void 0 : product.image,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"productdisplay-right\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: product === null || product === void 0 ? void 0 : product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"productdisplay-right-star\",\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: star_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: star_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: star_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: star_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: star_dull_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"(122)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"productdisplay-right-prices\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"productdisplay-right-price-old\",\n          children: [\"\\u20B9\", product.old_price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"productdisplay-right-price-new\",\n          children: [\"\\u20B9\", product.new_price]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"productdisplay-right-description\",\n        children: \"Lorem, ipsum dolor sit amet consectetur adipisicing elit. Odio eos amet ratione exercitationem asperiores, nisi corrupti doloribus. Commodi quisquam porro quas. Cumque odio aperiam accusamus mollitia ad numquam quia fugiat?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"productdisplay-right-size\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Select Size\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"productdisplay-right-sizes\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"S\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"M\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"L\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"XL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"XXL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => addToCart(product.id),\n        children: \"ADD TO CART\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"productdisplay-right-category\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Category:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 62\n        }, this), \"Women, T-Shirt, Crop Top \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"productdisplay-right-category\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Tags :\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 62\n        }, this), \"Modern, Latest \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 9\n  }, this);\n};\n_s(ProductDisplay, \"BhPAtey83megfPxjs0mJyhSxKaI=\");\n_c = ProductDisplay;\nexport default ProductDisplay;\nvar _c;\n$RefreshReg$(_c, \"ProductDisplay\");", "map": {"version": 3, "names": ["React", "useContext", "star_icon", "star_dull_icon", "ShopContext", "jsxDEV", "_jsxDEV", "ProductDisplay", "props", "_s", "product", "addToCart", "className", "children", "src", "image", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "old_price", "new_price", "onClick", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/ProductDisplay/ProductDisplay.jsx"], "sourcesContent": ["import React, { useContext } from 'react';\r\nimport './ProductDisplay.css';\r\nimport star_icon from \"../Asset/star_icon.png\";\r\nimport star_dull_icon from \"../Asset/star_dull_icon.png\";\r\nimport { ShopContext } from '../../Context/ShopContext';\r\n\r\nconst ProductDisplay = (props) => {\r\n    const { product } = props;\r\n    const { addToCart } = useContext(ShopContext);\r\n\r\n    return (\r\n        <div className=\"productdisplay\">\r\n            <div className=\"productdisplay-left\">\r\n                <div className=\"productdisplay-img-list\">\r\n                    <img src={product.image} alt=\"\" />\r\n                    <img src={product.image} alt=\"\" />\r\n                    <img src={product.image} alt=\"\" />\r\n                    <img src={product.image} alt=\"\" />\r\n                </div>\r\n                <div className=\"productdisplay-img\">\r\n                    <img className='productdisplay-main-img' src={product?.image} alt=\"\" />\r\n                </div>\r\n            </div>\r\n            <div className=\"productdisplay-right\">\r\n                <h1>{product?.name}</h1>\r\n                <div className=\"productdisplay-right-star\">\r\n                    <img src={star_icon} alt=\"\" />\r\n                    <img src={star_icon} alt=\"\" />\r\n                    <img src={star_icon} alt=\"\" />\r\n                    <img src={star_icon} alt=\"\" />\r\n                    <img src={star_dull_icon} alt=\"\" />\r\n                    <p>(122)</p>\r\n                </div>\r\n                <div className=\"productdisplay-right-prices\">\r\n                    <div className=\"productdisplay-right-price-old\">₹{product.old_price}</div>\r\n                    <div className=\"productdisplay-right-price-new\">₹{product.new_price}</div>\r\n                </div>\r\n                <div className=\"productdisplay-right-description\">\r\n                    Lorem, ipsum dolor sit amet consectetur adipisicing elit. Odio eos amet ratione exercitationem asperiores, nisi corrupti doloribus. Commodi quisquam porro quas. Cumque odio aperiam accusamus mollitia ad numquam quia fugiat?\r\n                </div>\r\n                <div className=\"productdisplay-right-size\">\r\n                    <h1>Select Size</h1>\r\n                    <div className='productdisplay-right-sizes'>\r\n                        <div>S</div>\r\n                        <div>M</div>\r\n                        <div>L</div>\r\n                        <div>XL</div>\r\n                        <div>XXL</div>\r\n                    </div>\r\n                </div>\r\n                <button onClick={() => addToCart(product.id)}>ADD TO CART</button>\r\n                <p className=\"productdisplay-right-category\"><span>Category:</span>Women, T-Shirt, Crop Top </p>\r\n                <p className=\"productdisplay-right-category\"><span>Tags :</span>Modern, Latest </p>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default ProductDisplay;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,QAAQ,OAAO;AACzC,OAAO,sBAAsB;AAC7B,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,cAAc,MAAM,6BAA6B;AACxD,SAASC,WAAW,QAAQ,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,cAAc,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAQ,CAAC,GAAGF,KAAK;EACzB,MAAM;IAAEG;EAAU,CAAC,GAAGV,UAAU,CAACG,WAAW,CAAC;EAE7C,oBACIE,OAAA;IAAKM,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BP,OAAA;MAAKM,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAChCP,OAAA;QAAKM,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACpCP,OAAA;UAAKQ,GAAG,EAAEJ,OAAO,CAACK,KAAM;UAACC,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClCd,OAAA;UAAKQ,GAAG,EAAEJ,OAAO,CAACK,KAAM;UAACC,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClCd,OAAA;UAAKQ,GAAG,EAAEJ,OAAO,CAACK,KAAM;UAACC,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClCd,OAAA;UAAKQ,GAAG,EAAEJ,OAAO,CAACK,KAAM;UAACC,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eACNd,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAC/BP,OAAA;UAAKM,SAAS,EAAC,yBAAyB;UAACE,GAAG,EAAEJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,KAAM;UAACC,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNd,OAAA;MAAKM,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjCP,OAAA;QAAAO,QAAA,EAAKH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEW;MAAI;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACxBd,OAAA;QAAKM,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACtCP,OAAA;UAAKQ,GAAG,EAAEZ,SAAU;UAACc,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9Bd,OAAA;UAAKQ,GAAG,EAAEZ,SAAU;UAACc,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9Bd,OAAA;UAAKQ,GAAG,EAAEZ,SAAU;UAACc,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9Bd,OAAA;UAAKQ,GAAG,EAAEZ,SAAU;UAACc,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9Bd,OAAA;UAAKQ,GAAG,EAAEX,cAAe;UAACa,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnCd,OAAA;UAAAO,QAAA,EAAG;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACNd,OAAA;QAAKM,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBACxCP,OAAA;UAAKM,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GAAC,QAAC,EAACH,OAAO,CAACY,SAAS;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1Ed,OAAA;UAAKM,SAAS,EAAC,gCAAgC;UAAAC,QAAA,GAAC,QAAC,EAACH,OAAO,CAACa,SAAS;QAAA;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC,eACNd,OAAA;QAAKM,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAElD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNd,OAAA;QAAKM,SAAS,EAAC,2BAA2B;QAAAC,QAAA,gBACtCP,OAAA;UAAAO,QAAA,EAAI;QAAW;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBd,OAAA;UAAKM,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACvCP,OAAA;YAAAO,QAAA,EAAK;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACZd,OAAA;YAAAO,QAAA,EAAK;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACZd,OAAA;YAAAO,QAAA,EAAK;UAAC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACZd,OAAA;YAAAO,QAAA,EAAK;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACbd,OAAA;YAAAO,QAAA,EAAK;UAAG;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNd,OAAA;QAAQkB,OAAO,EAAEA,CAAA,KAAMb,SAAS,CAACD,OAAO,CAACe,EAAE,CAAE;QAAAZ,QAAA,EAAC;MAAW;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAClEd,OAAA;QAAGM,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAACP,OAAA;UAAAO,QAAA,EAAM;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,6BAAyB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChGd,OAAA;QAAGM,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAACP,OAAA;UAAAO,QAAA,EAAM;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,mBAAe;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAX,EAAA,CAlDKF,cAAc;AAAAmB,EAAA,GAAdnB,cAAc;AAoDpB,eAAeA,cAAc;AAAC,IAAAmB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}