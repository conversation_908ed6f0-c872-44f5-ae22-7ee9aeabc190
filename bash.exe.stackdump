Stack trace:
Frame         Function      Args
0007FFFFA350  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF9250) msys-2.0.dll+0x1FEBA
0007FFFFA350  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA628) msys-2.0.dll+0x67F9
0007FFFFA350  000210046832 (000210285FF9, 0007FFFFA208, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA350  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFA350  0002100690B4 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA630  00021006A49D (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFF62AD0000 ntdll.dll
7FFF60950000 KERNEL32.DLL
7FFF600D0000 KERNELBASE.dll
7FFF588F0000 apphelp.dll
7FFF612F0000 USER32.dll
7FFF5FE40000 win32u.dll
7FFF61050000 GDI32.dll
7FFF5FBC0000 gdi32full.dll
000210040000 msys-2.0.dll
7FFF5FE70000 msvcp_win.dll
7FFF5FCF0000 ucrtbase.dll
7FFF61080000 advapi32.dll
7FFF607D0000 msvcrt.dll
7FFF61E10000 sechost.dll
7FFF5FE10000 bcrypt.dll
7FFF62520000 RPCRT4.dll
7FFF5F3B0000 CRYPTBASE.DLL
7FFF5FF90000 bcryptPrimitives.dll
7FFF62090000 IMM32.DLL
