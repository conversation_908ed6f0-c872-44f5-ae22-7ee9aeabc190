.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.loading-container.small {
    padding: 1rem;
}

.loading-container.large {
    padding: 4rem;
    min-height: 50vh;
}

.loading-spinner {
    position: relative;
    width: 60px;
    height: 60px;
}

.loading-container.small .loading-spinner {
    width: 30px;
    height: 30px;
}

.loading-container.large .loading-spinner {
    width: 80px;
    height: 80px;
}

.spinner-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top: 3px solid #ff4141;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
}

.spinner-ring:nth-child(1) {
    animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
    animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
    animation-delay: -0.15s;
}

.loading-text {
    margin-top: 1rem;
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

.loading-container.small .loading-text {
    font-size: 12px;
    margin-top: 0.5rem;
}

.loading-container.large .loading-text {
    font-size: 16px;
    margin-top: 1.5rem;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Skeleton loading for product cards */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.skeleton-product-card {
    width: 100%;
    height: 350px;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.skeleton-text {
    height: 20px;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-text.short {
    width: 60%;
}

.skeleton-text.medium {
    width: 80%;
}

.skeleton-text.long {
    width: 100%;
}

@keyframes loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}
