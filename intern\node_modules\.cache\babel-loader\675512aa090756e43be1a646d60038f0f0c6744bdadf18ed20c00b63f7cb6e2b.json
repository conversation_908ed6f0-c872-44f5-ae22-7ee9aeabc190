{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Hero\\\\Hero.jsx\";\nimport React from 'react';\nimport './Hero.css';\nimport hand_icon from '../Asset/hand_icon.png';\nimport arrow_icon from '../Asset/arrow.png';\nimport hero_image from '../Asset/hero_image.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"hero\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: [\"NEW ARRIVALS ONLY\", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-hand-icon\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: hand_icon,\n            alt: \"\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"New Collection\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"for everyone\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-latest-btn\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Latest Collection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: arrow_icon,\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-right\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: hero_image,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "hand_icon", "arrow_icon", "hero_image", "jsxDEV", "_jsxDEV", "Hero", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Hero/Hero.jsx"], "sourcesContent": ["import React from 'react';\r\nimport './Hero.css';\r\nimport hand_icon from '../Asset/hand_icon.png';\r\nimport arrow_icon from '../Asset/arrow.png';\r\nimport hero_image from '../Asset/hero_image.png';\r\n\r\nconst Hero = () => {\r\n    return (\r\n        <div className=\"hero\">\r\n\r\n\r\n\r\n            <div className=\"hero-left\">\r\n                <h2 >NEW ARRIVALS ONLY\r\n                    <div className=\"hero-hand-icon\">\r\n                        <img src={hand_icon} alt=\"\" />\r\n                    </div>\r\n                </h2>\r\n\r\n                <p>New Collection</p>\r\n                <p>for everyone</p>\r\n                <div className=\"hero-latest-btn\">\r\n                    <div>Latest Collection</div>\r\n                    <img src={arrow_icon} alt=\"\" />\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"hero-right\">\r\n                <img src={hero_image} alt=\"\" />\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Hero;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,YAAY;AACnB,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,UAAU,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACf,oBACID,OAAA;IAAKE,SAAS,EAAC,MAAM;IAAAC,QAAA,gBAIjBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACtBH,OAAA;QAAAG,QAAA,GAAK,mBACD,eAAAH,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC3BH,OAAA;YAAKI,GAAG,EAAER,SAAU;YAACS,GAAG,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAELT,OAAA;QAAAG,QAAA,EAAG;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrBT,OAAA;QAAAG,QAAA,EAAG;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACnBT,OAAA;QAAKE,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BH,OAAA;UAAAG,QAAA,EAAK;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5BT,OAAA;UAAKI,GAAG,EAAEP,UAAW;UAACQ,GAAG,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENT,OAAA;MAAKE,SAAS,EAAC,YAAY;MAAAC,QAAA,eACvBH,OAAA;QAAKI,GAAG,EAAEN,UAAW;QAACO,GAAG,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAC,EAAA,GA1BKT,IAAI;AA4BV,eAAeA,IAAI;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}