.wishlist-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 60vh;
}

.wishlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.wishlist-header h1 {
    color: #333;
    font-size: 2rem;
    font-weight: 600;
}

.wishlist-count {
    background: #ff4141;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.empty-wishlist {
    text-align: center;
    padding: 4rem 2rem;
}

.empty-heart-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-wishlist h2 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.empty-wishlist p {
    color: #666;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.continue-shopping-btn {
    background: #ff4141;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
    background: #e03131;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 65, 65, 0.3);
}

.wishlist-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
}

.wishlist-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.wishlist-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.wishlist-item-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.wishlist-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.wishlist-item:hover .wishlist-item-image img {
    transform: scale(1.05);
}

.remove-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.remove-wishlist-btn:hover {
    background: #ff4141;
    color: white;
    transform: scale(1.1);
}

.wishlist-item-details {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-prices {
    margin-bottom: 1rem;
}

.new-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #ff4141;
    margin-right: 0.5rem;
}

.old-price {
    font-size: 1rem;
    color: #999;
    text-decoration: line-through;
}

.wishlist-item-actions {
    display: flex;
    gap: 0.5rem;
}

.add-to-cart-btn {
    flex: 1;
    background: #ff4141;
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.add-to-cart-btn:hover {
    background: #e03131;
    transform: translateY(-1px);
}

.loading {
    text-align: center;
    padding: 4rem;
    font-size: 1.2rem;
    color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wishlist-container {
        padding: 1rem;
    }
    
    .wishlist-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .wishlist-header h1 {
        font-size: 1.5rem;
    }
    
    .wishlist-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .wishlist-item-image {
        height: 200px;
    }
    
    .wishlist-item-details {
        padding: 1rem;
    }
    
    .empty-wishlist {
        padding: 2rem 1rem;
    }
    
    .empty-heart-icon {
        font-size: 3rem;
    }
}
