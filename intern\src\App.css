/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  color: #333333;
  transition: all 0.3s ease;
}

body.dark-mode {
  background-color: #121212;
  color: #ffffff;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 0;
}

/* Dark mode for common elements */
body.dark-mode .popular,
body.dark-mode .new-Collection,
body.dark-mode .offers {
  background-color: #1a1a1a;
  color: #ffffff;
}

body.dark-mode .popular h1,
body.dark-mode .new-Collection h1 {
  color: #ffffff;
}

body.dark-mode .popular hr,
body.dark-mode .new-Collection hr {
  background: #ff4141;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

body.dark-mode ::-webkit-scrollbar-track {
  background: #2a2a2a;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

body.dark-mode ::-webkit-scrollbar-thumb {
  background: #555;
}

body.dark-mode ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Loading animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Button hover effects */
button {
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-2px);
}

/* Link styles */
a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #ff4141;
  outline-offset: 2px;
}

/* Responsive images */
img {
  max-width: 100%;
  height: auto;
}

/* Error and success messages */
.error-message {
  color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
}

.success-message {
  color: #28a745;
  background: rgba(40, 167, 69, 0.1);
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
}

body.dark-mode .error-message {
  background: rgba(220, 53, 69, 0.2);
}

body.dark-mode .success-message {
  background: rgba(40, 167, 69, 0.2);
}