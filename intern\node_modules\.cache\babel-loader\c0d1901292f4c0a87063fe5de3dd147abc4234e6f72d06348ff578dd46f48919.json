{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\App.js\";\nimport './App.css';\nimport Navbar from './Components/Navbar/Navbar';\nimport { BrowserRouter, Route, Routes } from 'react-router-dom';\nimport Shop from './Pages/shop';\nimport ShopCategory from './Pages/ShopCategory';\nimport Product from './Pages/Product';\nimport Cart from './Pages/Cart';\nimport LoginSignUp from './Pages/LoginSignUp';\nimport Footer from './Components/Footer/Footer';\nimport Wishlist from './Components/Wishlist/Wishlist';\nimport UserProfile from './Components/UserProfile/UserProfile';\nimport Portfolio from './Components/Portfolio/Portfolio';\nimport men_banner from './Components/Asset/banner_mens.png';\nimport women_banner from './Components/Asset/banner_women.png';\nimport kid_banner from './Components/Asset/banner_kids.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app\",\n    children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"main-content\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Shop, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/mens\",\n            element: /*#__PURE__*/_jsxDEV(ShopCategory, {\n              banner: men_banner,\n              category: \"men\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/womens\",\n            element: /*#__PURE__*/_jsxDEV(ShopCategory, {\n              banner: women_banner,\n              category: \"women\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/kids\",\n            element: /*#__PURE__*/_jsxDEV(ShopCategory, {\n              banner: kid_banner,\n              category: \"kid\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/product\",\n            element: /*#__PURE__*/_jsxDEV(Product, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 45\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Route, {\n              path: \":productId\",\n              element: /*#__PURE__*/_jsxDEV(Product, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 29,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/cart\",\n            element: /*#__PURE__*/_jsxDEV(Cart, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/wishlist\",\n            element: /*#__PURE__*/_jsxDEV(Wishlist, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/profile\",\n            element: /*#__PURE__*/_jsxDEV(UserProfile, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/portfolio\",\n            element: /*#__PURE__*/_jsxDEV(Portfolio, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginSignUp, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Route", "Routes", "Shop", "ShopCategory", "Product", "<PERSON><PERSON>", "LoginSignUp", "Footer", "Wishlist", "UserProfile", "Portfolio", "men_banner", "women_banner", "kid_banner", "jsxDEV", "_jsxDEV", "App", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "banner", "category", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/App.js"], "sourcesContent": ["import './App.css';\nimport Navbar from './Components/Navbar/Navbar';\nimport { BrowserRouter, Route, Routes } from 'react-router-dom';\nimport Shop from './Pages/shop';\nimport ShopCategory from './Pages/ShopCategory';\nimport Product from './Pages/Product';\nimport Cart from './Pages/Cart';\nimport LoginSignUp from './Pages/LoginSignUp';\nimport Footer from './Components/Footer/Footer';\nimport Wishlist from './Components/Wishlist/Wishlist';\nimport UserProfile from './Components/UserProfile/UserProfile';\nimport Portfolio from './Components/Portfolio/Portfolio';\nimport men_banner from './Components/Asset/banner_mens.png';\nimport women_banner from './Components/Asset/banner_women.png';\nimport kid_banner from './Components/Asset/banner_kids.png';\n\nfunction App() {\n  return (\n    <div className=\"app\">\n      <BrowserRouter>\n        <Navbar />\n        <main className=\"main-content\">\n          <Routes>\n            <Route path='/' element={<Shop />} />\n            <Route path='/mens' element={<ShopCategory banner={men_banner} category=\"men\" />} />\n            <Route path='/womens' element={<ShopCategory banner={women_banner} category=\"women\" />} />\n            <Route path='/kids' element={<ShopCategory banner={kid_banner} category=\"kid\" />} />\n            <Route path=\"/product\" element={<Product />}>\n              <Route path=':productId' element={<Product />} />\n            </Route>\n            <Route path='/cart' element={<Cart />} />\n            <Route path='/wishlist' element={<Wishlist />} />\n            <Route path='/profile' element={<UserProfile />} />\n            <Route path='/portfolio' element={<Portfolio />} />\n            <Route path='/login' element={<LoginSignUp />} />\n          </Routes>\n        </main>\n        <Footer />\n      </BrowserRouter>\n    </div>\n  );\n}\n\nexport default App;\n\n"], "mappings": ";AAAA,OAAO,WAAW;AAClB,OAAOA,MAAM,MAAM,4BAA4B;AAC/C,SAASC,aAAa,EAAEC,KAAK,EAAEC,MAAM,QAAQ,kBAAkB;AAC/D,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,WAAW,MAAM,sCAAsC;AAC9D,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,YAAY,MAAM,qCAAqC;AAC9D,OAAOC,UAAU,MAAM,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBH,OAAA,CAAChB,aAAa;MAAAmB,QAAA,gBACZH,OAAA,CAACjB,MAAM;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA;QAAME,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC5BH,OAAA,CAACd,MAAM;UAAAiB,QAAA,gBACLH,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACb,IAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrCP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAET,OAAA,CAACZ,YAAY;cAACsB,MAAM,EAAEd,UAAW;cAACe,QAAQ,EAAC;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,SAAS;YAACC,OAAO,eAAET,OAAA,CAACZ,YAAY;cAACsB,MAAM,EAAEb,YAAa;cAACc,QAAQ,EAAC;YAAO;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1FP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAET,OAAA,CAACZ,YAAY;cAACsB,MAAM,EAAEZ,UAAW;cAACa,QAAQ,EAAC;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpFP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACX,OAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,eAC1CH,OAAA,CAACf,KAAK;cAACuB,IAAI,EAAC,YAAY;cAACC,OAAO,eAAET,OAAA,CAACX,OAAO;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACRP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAET,OAAA,CAACV,IAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAET,OAAA,CAACP,QAAQ;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjDP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACN,WAAW;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,YAAY;YAACC,OAAO,eAAET,OAAA,CAACL,SAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDP,OAAA,CAACf,KAAK;YAACuB,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAET,OAAA,CAACT,WAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACPP,OAAA,CAACR,MAAM;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV;AAACK,EAAA,GAzBQX,GAAG;AA2BZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}