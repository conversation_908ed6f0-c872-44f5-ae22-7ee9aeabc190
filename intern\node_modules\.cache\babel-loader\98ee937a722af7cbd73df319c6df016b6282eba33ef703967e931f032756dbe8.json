{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Loading\\\\Loading.jsx\";\nimport React from 'react';\nimport './Loading.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Loading = ({\n  size = 'medium',\n  text = 'Loading...'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `loading-container ${size}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-ring\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this), text && /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"loading-text\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 22\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = Loading;\nexport default Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Loading", "size", "text", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Loading/Loading.jsx"], "sourcesContent": ["import React from 'react';\nimport './Loading.css';\n\nconst Loading = ({ size = 'medium', text = 'Loading...' }) => {\n    return (\n        <div className={`loading-container ${size}`}>\n            <div className=\"loading-spinner\">\n                <div className=\"spinner-ring\"></div>\n                <div className=\"spinner-ring\"></div>\n                <div className=\"spinner-ring\"></div>\n                <div className=\"spinner-ring\"></div>\n            </div>\n            {text && <p className=\"loading-text\">{text}</p>}\n        </div>\n    );\n};\n\nexport default Loading;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI,GAAG,QAAQ;EAAEC,IAAI,GAAG;AAAa,CAAC,KAAK;EAC1D,oBACIH,OAAA;IAAKI,SAAS,EAAG,qBAAoBF,IAAK,EAAE;IAAAG,QAAA,gBACxCL,OAAA;MAAKI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BL,OAAA;QAAKI,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCT,OAAA;QAAKI,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCT,OAAA;QAAKI,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACpCT,OAAA;QAAKI,SAAS,EAAC;MAAc;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,EACLN,IAAI,iBAAIH,OAAA;MAAGI,SAAS,EAAC,cAAc;MAAAC,QAAA,EAAEF;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC9C,CAAC;AAEd,CAAC;AAACC,EAAA,GAZIT,OAAO;AAcb,eAAeA,OAAO;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}