[{"C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Navbar\\Navbar.jsx": "4", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Shop.jsx": "5", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\ShopCategory.jsx": "6", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Product.jsx": "7", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Cart.jsx": "8", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\LoginSignUp.jsx": "9", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Hero\\Hero.jsx": "10", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\shop.jsx": "11", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Popular\\Popular.jsx": "12", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\data.js": "13", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Item\\Item.jsx": "14", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Offers\\Offers.jsx": "15", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewCollection\\NewCollection.jsx": "16", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\new_collections.js": "17", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewsLetter\\NewsLetter.jsx": "18", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Footer\\Footer.jsx": "19", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Context\\ShopContext.jsx": "20", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\all_product.js": "21", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Breadcrums\\Breadcrums.jsx": "22", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\ProductDisplay\\ProductDisplay.jsx": "23", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\DescriptionBox\\DescriptionBox.jsx": "24", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\RelatedProducts\\RelatedProducts.jsx": "25", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\CartItems\\CartItems.jsx": "26"}, {"size": 599, "mtime": 1704878105028, "results": "27", "hashOfConfig": "28"}, {"size": 1351, "mtime": 1709580640759, "results": "29", "hashOfConfig": "28"}, {"size": 362, "mtime": 1704692750161, "results": "30", "hashOfConfig": "28"}, {"size": 4787, "mtime": 1713772564805, "results": "31", "hashOfConfig": "28"}, {"size": 691, "mtime": 1704883600749, "results": "32", "hashOfConfig": "28"}, {"size": 1430, "mtime": 1704880940751, "results": "33", "hashOfConfig": "28"}, {"size": 998, "mtime": 1705734939022, "results": "34", "hashOfConfig": "28"}, {"size": 217, "mtime": 1705254707551, "results": "35", "hashOfConfig": "28"}, {"size": 3330, "mtime": 1706370031421, "results": "36", "hashOfConfig": "28"}, {"size": 954, "mtime": 1705735585943, "results": "37", "hashOfConfig": "28"}, {"size": 551, "mtime": 1705403309564, "results": "38", "hashOfConfig": "28"}, {"size": 1517, "mtime": 1709579804207, "results": "39", "hashOfConfig": "28"}, {"size": 814, "mtime": 1704693654052, "results": "40", "hashOfConfig": "28"}, {"size": 4116, "mtime": 1752946269690, "results": "41", "hashOfConfig": "28"}, {"size": 608, "mtime": 1704789745623, "results": "42", "hashOfConfig": "28"}, {"size": 872, "mtime": 1709580827299, "results": "43", "hashOfConfig": "28"}, {"size": 1633, "mtime": 1704693654195, "results": "44", "hashOfConfig": "28"}, {"size": 465, "mtime": 1704821292285, "results": "45", "hashOfConfig": "28"}, {"size": 1416, "mtime": 1706639747497, "results": "46", "hashOfConfig": "28"}, {"size": 3543, "mtime": 1706348099488, "results": "47", "hashOfConfig": "28"}, {"size": 7942, "mtime": 1704693653858, "results": "48", "hashOfConfig": "28"}, {"size": 437, "mtime": 1704900193386, "results": "49", "hashOfConfig": "28"}, {"size": 2863, "mtime": 1705219762430, "results": "50", "hashOfConfig": "28"}, {"size": 922, "mtime": 1705216790907, "results": "51", "hashOfConfig": "28"}, {"size": 634, "mtime": 1709579550313, "results": "52", "hashOfConfig": "28"}, {"size": 2730, "mtime": 1705253797422, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ym5idd", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Navbar\\Navbar.jsx", ["132"], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Shop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\ShopCategory.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Product.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Cart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\LoginSignUp.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Hero\\Hero.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\shop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Popular\\Popular.jsx", ["133"], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\data.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Item\\Item.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Offers\\Offers.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewCollection\\NewCollection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\new_collections.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewsLetter\\NewsLetter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Footer\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Context\\ShopContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\all_product.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Breadcrums\\Breadcrums.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\ProductDisplay\\ProductDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\DescriptionBox\\DescriptionBox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\RelatedProducts\\RelatedProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\CartItems\\CartItems.jsx", [], [], {"ruleId": "134", "severity": 1, "message": "135", "line": 81, "column": 11, "nodeType": "136", "messageId": "137", "endLine": 81, "endColumn": 25}, {"ruleId": "134", "severity": 1, "message": "138", "line": 3, "column": 8, "nodeType": "136", "messageId": "137", "endLine": 3, "endColumn": 30}, "no-unused-vars", "'toggleDarkMode' is assigned a value but never used.", "Identifier", "unusedVar", "'initialPopularProducts' is defined but never used."]