[{"C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Navbar\\Navbar.jsx": "4", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Shop.jsx": "5", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\ShopCategory.jsx": "6", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Product.jsx": "7", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Cart.jsx": "8", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\LoginSignUp.jsx": "9", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Hero\\Hero.jsx": "10", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\shop.jsx": "11", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Popular\\Popular.jsx": "12", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\data.js": "13", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Item\\Item.jsx": "14", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Offers\\Offers.jsx": "15", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewCollection\\NewCollection.jsx": "16", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\new_collections.js": "17", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewsLetter\\NewsLetter.jsx": "18", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Footer\\Footer.jsx": "19", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Context\\ShopContext.jsx": "20", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\all_product.js": "21", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Breadcrums\\Breadcrums.jsx": "22", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\ProductDisplay\\ProductDisplay.jsx": "23", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\DescriptionBox\\DescriptionBox.jsx": "24", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\RelatedProducts\\RelatedProducts.jsx": "25", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\CartItems\\CartItems.jsx": "26", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Wishlist\\Wishlist.jsx": "27", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\UserProfile\\UserProfile.jsx": "28", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Portfolio\\Portfolio.jsx": "29", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\SearchBar\\SearchBar.jsx": "30", "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Loading\\Loading.jsx": "31"}, {"size": 599, "mtime": 1704878105028, "results": "32", "hashOfConfig": "33"}, {"size": 1816, "mtime": 1752947199986, "results": "34", "hashOfConfig": "33"}, {"size": 362, "mtime": 1704692750161, "results": "35", "hashOfConfig": "33"}, {"size": 8191, "mtime": 1752949095384, "results": "36", "hashOfConfig": "33"}, {"size": 691, "mtime": 1704883600749, "results": "37", "hashOfConfig": "33"}, {"size": 1430, "mtime": 1704880940751, "results": "38", "hashOfConfig": "33"}, {"size": 998, "mtime": 1705734939022, "results": "39", "hashOfConfig": "33"}, {"size": 217, "mtime": 1705254707551, "results": "40", "hashOfConfig": "33"}, {"size": 3330, "mtime": 1706370031421, "results": "41", "hashOfConfig": "33"}, {"size": 954, "mtime": 1705735585943, "results": "42", "hashOfConfig": "33"}, {"size": 7074, "mtime": 1752948802122, "results": "43", "hashOfConfig": "33"}, {"size": 1456, "mtime": 1752948759010, "results": "44", "hashOfConfig": "33"}, {"size": 814, "mtime": 1704693654052, "results": "45", "hashOfConfig": "33"}, {"size": 4116, "mtime": 1752946269690, "results": "46", "hashOfConfig": "33"}, {"size": 608, "mtime": 1704789745623, "results": "47", "hashOfConfig": "33"}, {"size": 2315, "mtime": 1752948516526, "results": "48", "hashOfConfig": "33"}, {"size": 1633, "mtime": 1704693654195, "results": "49", "hashOfConfig": "33"}, {"size": 465, "mtime": 1704821292285, "results": "50", "hashOfConfig": "33"}, {"size": 1416, "mtime": 1706639747497, "results": "51", "hashOfConfig": "33"}, {"size": 3808, "mtime": 1752948445225, "results": "52", "hashOfConfig": "33"}, {"size": 7942, "mtime": 1704693653858, "results": "53", "hashOfConfig": "33"}, {"size": 437, "mtime": 1704900193386, "results": "54", "hashOfConfig": "33"}, {"size": 2863, "mtime": 1705219762430, "results": "55", "hashOfConfig": "33"}, {"size": 922, "mtime": 1705216790907, "results": "56", "hashOfConfig": "33"}, {"size": 634, "mtime": 1709579550313, "results": "57", "hashOfConfig": "33"}, {"size": 8517, "mtime": 1752946620363, "results": "58", "hashOfConfig": "33"}, {"size": 5253, "mtime": 1752946207264, "results": "59", "hashOfConfig": "33"}, {"size": 13346, "mtime": 1752947631794, "results": "60", "hashOfConfig": "33"}, {"size": 16894, "mtime": 1752947618591, "results": "61", "hashOfConfig": "33"}, {"size": 5240, "mtime": 1752948633726, "results": "62", "hashOfConfig": "33"}, {"size": 571, "mtime": 1752946133126, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ym5idd", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Navbar\\Navbar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Shop.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\ShopCategory.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Product.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\Cart.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\LoginSignUp.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Hero\\Hero.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Pages\\shop.jsx", [], ["157"], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Popular\\Popular.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\data.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Item\\Item.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Offers\\Offers.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewCollection\\NewCollection.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\new_collections.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\NewsLetter\\NewsLetter.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Footer\\Footer.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Context\\ShopContext.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Asset\\all_product.js", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Breadcrums\\Breadcrums.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\ProductDisplay\\ProductDisplay.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\DescriptionBox\\DescriptionBox.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\RelatedProducts\\RelatedProducts.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\CartItems\\CartItems.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Wishlist\\Wishlist.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\UserProfile\\UserProfile.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Portfolio\\Portfolio.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\SearchBar\\SearchBar.jsx", [], [], "C:\\Users\\<USER>\\Desktop\\e-Commerce\\intern\\src\\Components\\Loading\\Loading.jsx", [], [], {"ruleId": "158", "severity": 1, "message": "159", "line": 27, "column": 8, "nodeType": "160", "endLine": 27, "endColumn": 10, "suggestions": "161", "suppressions": "162"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleSearch'. Either include it or remove the dependency array.", "ArrayExpression", ["163"], ["164"], {"desc": "165", "fix": "166"}, {"kind": "167", "justification": "168"}, "Update the dependencies array to be: [handleSearch]", {"range": "169", "text": "170"}, "directive", "", [1220, 1222], "[handleSearch]"]