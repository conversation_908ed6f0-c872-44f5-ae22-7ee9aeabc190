{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\NewCollection\\\\NewCollection.jsx\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport './NewCollection.css';\nimport Item from '../Item/Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewCollection = () => {\n  _s();\n  const [new_collection, setNew_collection] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    setLoading(true);\n    fetch('http://localhost:3000/newcollections').then(response => {\n      if (!response.ok) {\n        throw new Error(`HTTP error! Status: ${response.status}`);\n      }\n      return response.json();\n    }).then(data => {\n      console.log(\"New collections fetched:\", data);\n      setNew_collection(data);\n      setLoading(false);\n    }).catch(error => {\n      console.error(\"Error fetching new collections:\", error);\n      setError(error);\n      setLoading(false);\n    });\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"new-Collection\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"NEW COLLECTIONS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collection\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Loading new collections...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 13\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"new-Collection\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"NEW COLLECTIONS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"collection\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Error loading collections: \", error.message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"new-Collection\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"NEW COLLECTIONS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"collection\",\n      children: new_collection.length > 0 ? new_collection.map((item, i) => {\n        return /*#__PURE__*/_jsxDEV(Item, {\n          id: item.id,\n          name: item.name,\n          image: item.image,\n          new_price: item.new_price,\n          old_price: item.old_price\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 32\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No new collections available at the moment.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 9\n  }, this);\n};\n_s(NewCollection, \"bl001kxfQPYg1sZvbZftqjiJV+4=\");\n_c = NewCollection;\nexport default NewCollection;\nvar _c;\n$RefreshReg$(_c, \"NewCollection\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "NewCollection", "_s", "new_collection", "setNew_collection", "loading", "setLoading", "error", "setError", "fetch", "then", "response", "ok", "Error", "status", "json", "data", "console", "log", "catch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "length", "map", "item", "i", "id", "name", "image", "new_price", "old_price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/NewCollection/NewCollection.jsx"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport './NewCollection.css';\r\nimport Item from '../Item/Item';\r\n\r\nconst NewCollection = () => {\r\n    const [new_collection, setNew_collection] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    useEffect(() => {\r\n        setLoading(true);\r\n        fetch('http://localhost:3000/newcollections')\r\n            .then((response) => {\r\n                if (!response.ok) {\r\n                    throw new Error(`HTTP error! Status: ${response.status}`);\r\n                }\r\n                return response.json();\r\n            })\r\n            .then((data) => {\r\n                console.log(\"New collections fetched:\", data);\r\n                setNew_collection(data);\r\n                setLoading(false);\r\n            })\r\n            .catch((error) => {\r\n                console.error(\"Error fetching new collections:\", error);\r\n                setError(error);\r\n                setLoading(false);\r\n            });\r\n    }, []);\r\n\r\n    if (loading) {\r\n        return (\r\n            <div className=\"new-Collection\">\r\n                <h1>NEW COLLECTIONS</h1>\r\n                <hr />\r\n                <div className=\"collection\">\r\n                    <p>Loading new collections...</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    if (error) {\r\n        return (\r\n            <div className=\"new-Collection\">\r\n                <h1>NEW COLLECTIONS</h1>\r\n                <hr />\r\n                <div className=\"collection\">\r\n                    <p>Error loading collections: {error.message}</p>\r\n                </div>\r\n            </div>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <div className=\"new-Collection\">\r\n            <h1>NEW COLLECTIONS</h1>\r\n            <hr />\r\n            <div className=\"collection\">\r\n                {new_collection.length > 0 ? (\r\n                    new_collection.map((item, i) => {\r\n                        return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price} />\r\n                    })\r\n                ) : (\r\n                    <p>No new collections available at the moment.</p>\r\n                )}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default NewCollection;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,OAAO,qBAAqB;AAC5B,OAAOC,IAAI,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACZS,UAAU,CAAC,IAAI,CAAC;IAChBG,KAAK,CAAC,sCAAsC,CAAC,CACxCC,IAAI,CAAEC,QAAQ,IAAK;MAChB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAE,uBAAsBF,QAAQ,CAACG,MAAO,EAAC,CAAC;MAC7D;MACA,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CACDL,IAAI,CAAEM,IAAI,IAAK;MACZC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,IAAI,CAAC;MAC7CZ,iBAAiB,CAACY,IAAI,CAAC;MACvBV,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CACDa,KAAK,CAAEZ,KAAK,IAAK;MACdU,OAAO,CAACV,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDC,QAAQ,CAACD,KAAK,CAAC;MACfD,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAEN,IAAID,OAAO,EAAE;IACT,oBACIL,OAAA;MAAKoB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BrB,OAAA;QAAAqB,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBzB,OAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,YAAY;QAAAC,QAAA,eACvBrB,OAAA;UAAAqB,QAAA,EAAG;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,IAAIlB,KAAK,EAAE;IACP,oBACIP,OAAA;MAAKoB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BrB,OAAA;QAAAqB,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBzB,OAAA;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNzB,OAAA;QAAKoB,SAAS,EAAC,YAAY;QAAAC,QAAA,eACvBrB,OAAA;UAAAqB,QAAA,GAAG,6BAA2B,EAACd,KAAK,CAACmB,OAAO;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd;EAEA,oBACIzB,OAAA;IAAKoB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BrB,OAAA;MAAAqB,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBzB,OAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNzB,OAAA;MAAKoB,SAAS,EAAC,YAAY;MAAAC,QAAA,EACtBlB,cAAc,CAACwB,MAAM,GAAG,CAAC,GACtBxB,cAAc,CAACyB,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;QAC5B,oBAAO9B,OAAA,CAACF,IAAI;UAASiC,EAAE,EAAEF,IAAI,CAACE,EAAG;UAACC,IAAI,EAAEH,IAAI,CAACG,IAAK;UAACC,KAAK,EAAEJ,IAAI,CAACI,KAAM;UAACC,SAAS,EAAEL,IAAI,CAACK,SAAU;UAACC,SAAS,EAAEN,IAAI,CAACM;QAAU,GAAzGL,CAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0G,CAAC;MAClI,CAAC,CAAC,gBAEFzB,OAAA;QAAAqB,QAAA,EAAG;MAA2C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IACpD;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACvB,EAAA,CAjEID,aAAa;AAAAmC,EAAA,GAAbnC,aAAa;AAmEnB,eAAeA,aAAa;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}