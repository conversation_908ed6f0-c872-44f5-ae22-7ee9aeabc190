{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Popular\\\\Popular.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './Popular.css';\nimport Item from '../Item/Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Popular = () => {\n  _s();\n  const [popularProducts, setPopularProducts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetch('http://localhost:3000/popularinwomen').then(response => {\n      if (!response.ok) {\n        throw new Error(`HTTP error! Status: ${response.status}`);\n      }\n      return response.json();\n    }).then(data => {\n      setPopularProducts(data);\n      setLoading(false);\n    }).catch(error => {\n      setError(error);\n      setLoading(false);\n    });\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 16\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Error: \", error.message]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 16\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"popular\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"POPULAR IN WOMEN\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"popular-item\",\n      children: popularProducts.map(item => /*#__PURE__*/_jsxDEV(Item, {\n        id: item.id,\n        name: item.name,\n        image: item.image,\n        new_price: item.new_price,\n        old_price: item.old_price\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 9\n  }, this);\n};\n_s(Popular, \"1x0pIb3e4K/n11efWvTVzI41MvQ=\");\n_c = Popular;\nexport default Popular;\nvar _c;\n$RefreshReg$(_c, \"Popular\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Popular", "_s", "popularProducts", "setPopularProducts", "loading", "setLoading", "error", "setError", "fetch", "then", "response", "ok", "Error", "status", "json", "data", "catch", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "className", "map", "item", "id", "name", "image", "new_price", "old_price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Popular/Popular.jsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport './Popular.css';\r\n\r\nimport Item from '../Item/Item';\r\n\r\nconst Popular = () => {\r\n    const [popularProducts, setPopularProducts] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n    useEffect(() => {\r\n        fetch('http://localhost:3000/popularinwomen')\r\n            .then((response) => {\r\n                if (!response.ok) {\r\n                    throw new Error(`HTTP error! Status: ${response.status}`);\r\n                }\r\n                return response.json();\r\n            })\r\n            .then((data) => {\r\n                setPopularProducts(data);\r\n                setLoading(false);\r\n            })\r\n            .catch((error) => {\r\n                setError(error);\r\n                setLoading(false);\r\n            });\r\n    }, []);\r\n\r\n\r\n    if (loading) {\r\n        return <p>Loading...</p>;\r\n    }\r\n\r\n    if (error) {\r\n        return <p>Error: {error.message}</p>;\r\n    }\r\n\r\n    return (\r\n        <div className=\"popular\">\r\n            <h1>POPULAR IN WOMEN</h1>\r\n            <hr />\r\n            <div className=\"popular-item\">\r\n                {popularProducts.map((item) => (\r\n                    <Item key={item.id} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price} />\r\n                ))}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Popular;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,eAAe;AAEtB,OAAOC,IAAI,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACQ,OAAO,EAAEC,UAAU,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EACxCD,SAAS,CAAC,MAAM;IACZa,KAAK,CAAC,sCAAsC,CAAC,CACxCC,IAAI,CAAEC,QAAQ,IAAK;MAChB,IAAI,CAACA,QAAQ,CAACC,EAAE,EAAE;QACd,MAAM,IAAIC,KAAK,CAAE,uBAAsBF,QAAQ,CAACG,MAAO,EAAC,CAAC;MAC7D;MACA,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC,CACDL,IAAI,CAAEM,IAAI,IAAK;MACZZ,kBAAkB,CAACY,IAAI,CAAC;MACxBV,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC,CACDW,KAAK,CAAEV,KAAK,IAAK;MACdC,QAAQ,CAACD,KAAK,CAAC;MACfD,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC,CAAC;EACV,CAAC,EAAE,EAAE,CAAC;EAGN,IAAID,OAAO,EAAE;IACT,oBAAOL,OAAA;MAAAkB,QAAA,EAAG;IAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAC5B;EAEA,IAAIf,KAAK,EAAE;IACP,oBAAOP,OAAA;MAAAkB,QAAA,GAAG,SAAO,EAACX,KAAK,CAACgB,OAAO;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EACxC;EAEA,oBACItB,OAAA;IAAKwB,SAAS,EAAC,SAAS;IAAAN,QAAA,gBACpBlB,OAAA;MAAAkB,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBtB,OAAA;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNtB,OAAA;MAAKwB,SAAS,EAAC,cAAc;MAAAN,QAAA,EACxBf,eAAe,CAACsB,GAAG,CAAEC,IAAI,iBACtB1B,OAAA,CAACF,IAAI;QAAe6B,EAAE,EAAED,IAAI,CAACC,EAAG;QAACC,IAAI,EAAEF,IAAI,CAACE,IAAK;QAACC,KAAK,EAAEH,IAAI,CAACG,KAAM;QAACC,SAAS,EAAEJ,IAAI,CAACI,SAAU;QAACC,SAAS,EAAEL,IAAI,CAACK;MAAU,GAA/GL,IAAI,CAACC,EAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAA0G,CAC/H;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACpB,EAAA,CA1CID,OAAO;AAAA+B,EAAA,GAAP/B,OAAO;AA4Cb,eAAeA,OAAO;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}