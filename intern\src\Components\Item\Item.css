.item {
    width: 280px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.item-image-container {
    position: relative;
    overflow: hidden;
    height: 280px;
}

.item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.item:hover img {
    transform: scale(1.1);
}

.item-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.item:hover .item-overlay {
    opacity: 1;
}

.wishlist-btn,
.quick-add-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.wishlist-btn:hover,
.quick-add-btn:hover {
    background: #ff4141;
    color: white;
    transform: scale(1.1);
}

.wishlist-btn.active {
    background: #ff4141;
    color: white;
}

.discount-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff4141;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.item-details {
    padding: 15px;
}

.item-name {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.item-rating {
    display: flex;
    align-items: center;
    gap: 2px;
    margin-bottom: 8px;
}

.star-filled {
    color: #ffc107;
    font-size: 12px;
}

.star-empty {
    color: #e0e0e0;
    font-size: 12px;
}

.rating-count {
    font-size: 12px;
    color: #666;
    margin-left: 4px;
}

.item-prices {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-price-new {
    color: #ff4141;
    font-size: 18px;
    font-weight: 700;
}

.item-price-old {
    color: #999;
    font-size: 14px;
    font-weight: 500;
    text-decoration: line-through;
}

/* Responsive Design */
@media (max-width: 1280px) {
    .item {
        width: 220px;
    }

    .item-image-container {
        height: 220px;
    }

    .item-name {
        font-size: 14px;
    }

    .item-price-new {
        font-size: 16px;
    }

    .item-price-old {
        font-size: 12px;
    }

    .item-details {
        padding: 12px;
    }
}

@media (max-width: 1024px) {
    .item {
        width: 200px;
    }

    .item-image-container {
        height: 200px;
    }

    .item-name {
        font-size: 14px;
    }

    .item-price-new {
        font-size: 15px;
    }

    .item-price-old {
        font-size: 12px;
    }

    .wishlist-btn,
    .quick-add-btn {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 800px) {
    .item {
        width: 180px;
    }

    .item-image-container {
        height: 180px;
    }

    .item-name {
        font-size: 13px;
    }

    .item-price-new {
        font-size: 14px;
    }

    .item-price-old {
        font-size: 11px;
    }

    .item-details {
        padding: 10px;
    }

    .item-overlay {
        opacity: 1;
        /* Always show on mobile */
    }

    .wishlist-btn,
    .quick-add-btn {
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 500px) {
    .item {
        width: 160px;
    }

    .item-image-container {
        height: 160px;
    }

    .item-name {
        font-size: 12px;
    }

    .item-price-new {
        font-size: 13px;
    }

    .item-price-old {
        font-size: 10px;
    }

    .item-details {
        padding: 8px;
    }

    .discount-badge {
        font-size: 10px;
        padding: 2px 6px;
    }

    .star-filled,
    .star-empty {
        font-size: 10px;
    }

    .rating-count {
        font-size: 10px;
    }
}