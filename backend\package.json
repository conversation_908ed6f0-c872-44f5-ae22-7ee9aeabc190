{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test-db": "node setup-mongodb.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.1.0", "multer": "^1.4.5-lts.1"}}