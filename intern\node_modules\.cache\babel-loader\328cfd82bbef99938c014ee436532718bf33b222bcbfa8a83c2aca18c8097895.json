{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Item\\\\Item.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useContext } from 'react';\nimport { Link } from 'react-router-dom';\nimport { FaHeart, FaShoppingCart, FaStar } from 'react-icons/fa';\nimport { ShopContext } from '../../Context/ShopContext';\nimport './Item.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Item = props => {\n  _s();\n  const [isWishlisted, setIsWishlisted] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    addToCart\n  } = useContext(ShopContext);\n  const handleWishlistToggle = async e => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!localStorage.getItem('auth-token')) {\n      alert('Please login to add items to wishlist');\n      return;\n    }\n    setIsLoading(true);\n    try {\n      const endpoint = isWishlisted ? 'removefromwishlist' : 'addtowishlist';\n      await fetch(`http://localhost:3000/${endpoint}`, {\n        method: 'POST',\n        headers: {\n          Accept: 'application/json',\n          'auth-token': `${localStorage.getItem('auth-token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          itemId: props.id\n        })\n      });\n      setIsWishlisted(!isWishlisted);\n    } catch (error) {\n      console.error('Error updating wishlist:', error);\n    }\n    setIsLoading(false);\n  };\n  const handleAddToCart = e => {\n    e.preventDefault();\n    e.stopPropagation();\n    addToCart(props.id);\n  };\n  const handleImageClick = () => {\n    window.scrollTo(0, 0);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"item\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"item-image-container\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: `/product/${props.id}`,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          onClick: handleImageClick,\n          src: props.image,\n          alt: props.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"item-overlay\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `wishlist-btn ${isWishlisted ? 'active' : ''}`,\n          onClick: handleWishlistToggle,\n          disabled: isLoading,\n          title: isWishlisted ? 'Remove from wishlist' : 'Add to wishlist',\n          children: /*#__PURE__*/_jsxDEV(FaHeart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"quick-add-btn\",\n          onClick: handleAddToCart,\n          title: \"Add to cart\",\n          children: /*#__PURE__*/_jsxDEV(FaShoppingCart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this), props.discount && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"discount-badge\",\n        children: [\"-\", Math.round((props.old_price - props.new_price) / props.old_price * 100), \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"item-details\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: `/product/${props.id}`,\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"item-name\",\n          children: props.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"item-rating\",\n          children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FaStar, {\n            className: i < (props.rating || 4) ? 'star-filled' : 'star-empty'\n          }, i, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 29\n          }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"rating-count\",\n            children: [\"(\", props.reviewCount || 0, \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"item-prices\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-price-new\",\n            children: [\"\\u20B9\", props.new_price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 25\n          }, this), props.old_price && props.old_price !== props.new_price && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"item-price-old\",\n            children: [\"\\u20B9\", props.old_price]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 9\n  }, this);\n};\n_s(Item, \"MxgVIMBrNg5XzdVUuy/a04Lt3Dc=\");\n_c = Item;\nexport default Item;\nvar _c;\n$RefreshReg$(_c, \"Item\");", "map": {"version": 3, "names": ["React", "useState", "useContext", "Link", "FaHeart", "FaShoppingCart", "FaStar", "ShopContext", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "props", "_s", "isWishlisted", "setIsWishlisted", "isLoading", "setIsLoading", "addToCart", "handleWishlistToggle", "e", "preventDefault", "stopPropagation", "localStorage", "getItem", "alert", "endpoint", "fetch", "method", "headers", "Accept", "body", "JSON", "stringify", "itemId", "id", "error", "console", "handleAddToCart", "handleImageClick", "window", "scrollTo", "className", "children", "to", "onClick", "src", "image", "alt", "name", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "disabled", "title", "discount", "Math", "round", "old_price", "new_price", "Array", "map", "_", "i", "rating", "reviewCount", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Item/Item.jsx"], "sourcesContent": ["import React, { useState, useContext } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { FaHeart, FaShoppingCart, FaStar } from 'react-icons/fa';\r\nimport { ShopContext } from '../../Context/ShopContext';\r\nimport './Item.css';\r\n\r\nconst Item = (props) => {\r\n    const [isWishlisted, setIsWishlisted] = useState(false);\r\n    const [isLoading, setIsLoading] = useState(false);\r\n    const { addToCart } = useContext(ShopContext);\r\n\r\n    const handleWishlistToggle = async (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n\r\n        if (!localStorage.getItem('auth-token')) {\r\n            alert('Please login to add items to wishlist');\r\n            return;\r\n        }\r\n\r\n        setIsLoading(true);\r\n        try {\r\n            const endpoint = isWishlisted ? 'removefromwishlist' : 'addtowishlist';\r\n            await fetch(`http://localhost:3000/${endpoint}`, {\r\n                method: 'POST',\r\n                headers: {\r\n                    Accept: 'application/json',\r\n                    'auth-token': `${localStorage.getItem('auth-token')}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: JSON.stringify({ itemId: props.id }),\r\n            });\r\n            setIsWishlisted(!isWishlisted);\r\n        } catch (error) {\r\n            console.error('Error updating wishlist:', error);\r\n        }\r\n        setIsLoading(false);\r\n    };\r\n\r\n    const handleAddToCart = (e) => {\r\n        e.preventDefault();\r\n        e.stopPropagation();\r\n        addToCart(props.id);\r\n    };\r\n\r\n    const handleImageClick = () => {\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    return (\r\n        <div className='item'>\r\n            <div className=\"item-image-container\">\r\n                <Link to={`/product/${props.id}`}>\r\n                    <img onClick={handleImageClick} src={props.image} alt={props.name} />\r\n                </Link>\r\n                <div className=\"item-overlay\">\r\n                    <button\r\n                        className={`wishlist-btn ${isWishlisted ? 'active' : ''}`}\r\n                        onClick={handleWishlistToggle}\r\n                        disabled={isLoading}\r\n                        title={isWishlisted ? 'Remove from wishlist' : 'Add to wishlist'}\r\n                    >\r\n                        <FaHeart />\r\n                    </button>\r\n                    <button\r\n                        className=\"quick-add-btn\"\r\n                        onClick={handleAddToCart}\r\n                        title=\"Add to cart\"\r\n                    >\r\n                        <FaShoppingCart />\r\n                    </button>\r\n                </div>\r\n                {props.discount && (\r\n                    <div className=\"discount-badge\">\r\n                        -{Math.round(((props.old_price - props.new_price) / props.old_price) * 100)}%\r\n                    </div>\r\n                )}\r\n            </div>\r\n\r\n            <div className=\"item-details\">\r\n                <Link to={`/product/${props.id}`}>\r\n                    <p className=\"item-name\">{props.name}</p>\r\n                    <div className=\"item-rating\">\r\n                        {[...Array(5)].map((_, i) => (\r\n                            <FaStar\r\n                                key={i}\r\n                                className={i < (props.rating || 4) ? 'star-filled' : 'star-empty'}\r\n                            />\r\n                        ))}\r\n                        <span className=\"rating-count\">({props.reviewCount || 0})</span>\r\n                    </div>\r\n                    <div className=\"item-prices\">\r\n                        <div className=\"item-price-new\">\r\n                            ₹{props.new_price}\r\n                        </div>\r\n                        {props.old_price && props.old_price !== props.new_price && (\r\n                            <div className=\"item-price-old\">\r\n                                ₹{props.old_price}\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </Link>\r\n            </div>\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Item;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,UAAU,QAAQ,OAAO;AACnD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,EAAEC,cAAc,EAAEC,MAAM,QAAQ,gBAAgB;AAChE,SAASC,WAAW,QAAQ,2BAA2B;AACvD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,MAAMC,IAAI,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACpB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEgB;EAAU,CAAC,GAAGf,UAAU,CAACK,WAAW,CAAC;EAE7C,MAAMW,oBAAoB,GAAG,MAAOC,CAAC,IAAK;IACtCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IAEnB,IAAI,CAACC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACrCC,KAAK,CAAC,uCAAuC,CAAC;MAC9C;IACJ;IAEAR,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACA,MAAMS,QAAQ,GAAGZ,YAAY,GAAG,oBAAoB,GAAG,eAAe;MACtE,MAAMa,KAAK,CAAE,yBAAwBD,QAAS,EAAC,EAAE;QAC7CE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACLC,MAAM,EAAE,kBAAkB;UAC1B,YAAY,EAAG,GAAEP,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,EAAC;UACrD,cAAc,EAAE;QACpB,CAAC;QACDO,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEC,MAAM,EAAEtB,KAAK,CAACuB;QAAG,CAAC;MAC7C,CAAC,CAAC;MACFpB,eAAe,CAAC,CAACD,YAAY,CAAC;IAClC,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD;IACAnB,YAAY,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMqB,eAAe,GAAIlB,CAAC,IAAK;IAC3BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IACnBJ,SAAS,CAACN,KAAK,CAACuB,EAAE,CAAC;EACvB,CAAC;EAED,MAAMI,gBAAgB,GAAGA,CAAA,KAAM;IAC3BC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACzB,CAAC;EAED,oBACI/B,OAAA;IAAKgC,SAAS,EAAC,MAAM;IAAAC,QAAA,gBACjBjC,OAAA;MAAKgC,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjCjC,OAAA,CAACN,IAAI;QAACwC,EAAE,EAAG,YAAWhC,KAAK,CAACuB,EAAG,EAAE;QAAAQ,QAAA,eAC7BjC,OAAA;UAAKmC,OAAO,EAAEN,gBAAiB;UAACO,GAAG,EAAElC,KAAK,CAACmC,KAAM;UAACC,GAAG,EAAEpC,KAAK,CAACqC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACP3C,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBjC,OAAA;UACIgC,SAAS,EAAG,gBAAe5B,YAAY,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC1D+B,OAAO,EAAE1B,oBAAqB;UAC9BmC,QAAQ,EAAEtC,SAAU;UACpBuC,KAAK,EAAEzC,YAAY,GAAG,sBAAsB,GAAG,iBAAkB;UAAA6B,QAAA,eAEjEjC,OAAA,CAACL,OAAO;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACT3C,OAAA;UACIgC,SAAS,EAAC,eAAe;UACzBG,OAAO,EAAEP,eAAgB;UACzBiB,KAAK,EAAC,aAAa;UAAAZ,QAAA,eAEnBjC,OAAA,CAACJ,cAAc;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EACLzC,KAAK,CAAC4C,QAAQ,iBACX9C,OAAA;QAAKgC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,GAAC,GAC3B,EAACc,IAAI,CAACC,KAAK,CAAE,CAAC9C,KAAK,CAAC+C,SAAS,GAAG/C,KAAK,CAACgD,SAAS,IAAIhD,KAAK,CAAC+C,SAAS,GAAI,GAAG,CAAC,EAAC,GAChF;MAAA;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEN3C,OAAA;MAAKgC,SAAS,EAAC,cAAc;MAAAC,QAAA,eACzBjC,OAAA,CAACN,IAAI;QAACwC,EAAE,EAAG,YAAWhC,KAAK,CAACuB,EAAG,EAAE;QAAAQ,QAAA,gBAC7BjC,OAAA;UAAGgC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAE/B,KAAK,CAACqC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzC3C,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAC,QAAA,GACvB,CAAC,GAAGkB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACpBtD,OAAA,CAACH,MAAM;YAEHmC,SAAS,EAAEsB,CAAC,IAAIpD,KAAK,CAACqD,MAAM,IAAI,CAAC,CAAC,GAAG,aAAa,GAAG;UAAa,GAD7DD,CAAC;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAET,CACJ,CAAC,eACF3C,OAAA;YAAMgC,SAAS,EAAC,cAAc;YAAAC,QAAA,GAAC,GAAC,EAAC/B,KAAK,CAACsD,WAAW,IAAI,CAAC,EAAC,GAAC;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACN3C,OAAA;UAAKgC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACxBjC,OAAA;YAAKgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,QAC3B,EAAC/B,KAAK,CAACgD,SAAS;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,EACLzC,KAAK,CAAC+C,SAAS,IAAI/C,KAAK,CAAC+C,SAAS,KAAK/C,KAAK,CAACgD,SAAS,iBACnDlD,OAAA;YAAKgC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,GAAC,QAC3B,EAAC/B,KAAK,CAAC+C,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAxC,EAAA,CAnGKF,IAAI;AAAAwD,EAAA,GAAJxD,IAAI;AAqGV,eAAeA,IAAI;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}