{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\DescriptionBox\\\\DescriptionBox.jsx\";\nimport React from 'react';\nimport './DescriptionBox.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DescriptionBox = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"descriptionbox\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"descriptionbox-navigator\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"descriptionbox-nav-box\",\n        children: \"Description\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"descriptionbox-nav-box-fade\",\n        children: \"Reviews\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"descriptionbox-description\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"In e-commerce websites, each product may have a description box that provides details about the product, its features, specifications, and any other relevant information.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \" When filling out forms online, some fields may have a description box to give users additional instructions or explanations about what information is expected in that field.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 9\n  }, this);\n};\n_c = DescriptionBox;\nexport default DescriptionBox;\nvar _c;\n$RefreshReg$(_c, \"DescriptionBox\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DescriptionBox", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/DescriptionBox/DescriptionBox.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './DescriptionBox.css'\r\nconst DescriptionBox = () => {\r\n    return (\r\n        <div className=\"descriptionbox\">\r\n            <div className=\"descriptionbox-navigator\">\r\n                <div className=\"descriptionbox-nav-box\">Description</div>\r\n                <div className=\"descriptionbox-nav-box-fade\">Reviews</div>\r\n            </div>\r\n            <div className=\"descriptionbox-description\">\r\n                <p>In e-commerce websites, each product may have a description box that provides details about the product, its features, specifications, and any other relevant information.\r\n                </p>\r\n                <p> When filling out forms online, some fields may have a description box to give users additional instructions or explanations about what information is expected in that field.</p>\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default DescriptionBox\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,sBAAsB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAC7B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EACzB,oBACID,OAAA;IAAKE,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BH,OAAA;MAAKE,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBACrCH,OAAA;QAAKE,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACzDP,OAAA;QAAKE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACvCH,OAAA;QAAAG,QAAA,EAAG;MACH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAAG,QAAA,EAAG;MAA8K;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAC,EAAA,GAdKP,cAAc;AAgBpB,eAAeA,cAAc;AAAA,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}