{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Portfolio\\\\Portfolio.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { FaReact, FaNodeJs, FaDatabase, FaGithub, FaExternalLinkAlt, FaCode, FaMobile, FaShoppingCart, FaUsers, FaSearch, FaHeart, FaPalette, FaShieldAlt } from 'react-icons/fa';\nimport { SiMongodb, SiExpress, SiJavascript, SiCss3, SiHtml5 } from 'react-icons/si';\nimport './Portfolio.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Portfolio = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('overview');\n  const technologies = [{\n    name: 'React.js',\n    icon: /*#__PURE__*/_jsxDEV(FaReact, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 35\n    }, this),\n    description: 'Frontend framework for building user interfaces'\n  }, {\n    name: 'Node.js',\n    icon: /*#__PURE__*/_jsxDEV(FaNodeJs, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 34\n    }, this),\n    description: 'Backend runtime environment'\n  }, {\n    name: 'Express.js',\n    icon: /*#__PURE__*/_jsxDEV(SiExpress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 37\n    }, this),\n    description: 'Web application framework for Node.js'\n  }, {\n    name: 'MongoDB',\n    icon: /*#__PURE__*/_jsxDEV(SiMongodb, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 34\n    }, this),\n    description: 'NoSQL database for data storage'\n  }, {\n    name: 'JavaScript',\n    icon: /*#__PURE__*/_jsxDEV(SiJavascript, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 37\n    }, this),\n    description: 'Programming language for web development'\n  }, {\n    name: 'CSS3',\n    icon: /*#__PURE__*/_jsxDEV(SiCss3, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 31\n    }, this),\n    description: 'Styling and responsive design'\n  }, {\n    name: 'HTML5',\n    icon: /*#__PURE__*/_jsxDEV(SiHtml5, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 32\n    }, this),\n    description: 'Markup language for web structure'\n  }];\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(FaShoppingCart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 19\n    }, this),\n    title: 'Advanced Shopping Cart',\n    description: 'Dynamic cart with quantity management, promo codes, and real-time total calculation'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaHeart, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 19\n    }, this),\n    title: 'Wishlist System',\n    description: 'Save favorite products with persistent storage and easy cart integration'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaSearch, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 19\n    }, this),\n    title: 'Smart Search & Filters',\n    description: 'Advanced product search with category, price, and rating filters'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaUsers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 19\n    }, this),\n    title: 'User Management',\n    description: 'Complete user authentication, profiles, and order history tracking'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaMobile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 19\n    }, this),\n    title: 'Responsive Design',\n    description: 'Fully responsive layout optimized for all device sizes'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaPalette, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 19\n    }, this),\n    title: 'Dark Mode',\n    description: 'Toggle between light and dark themes with smooth transitions'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 19\n    }, this),\n    title: 'Secure Authentication',\n    description: 'JWT-based authentication with protected routes and user sessions'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FaCode, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 19\n    }, this),\n    title: 'Modern UI/UX',\n    description: 'Contemporary design with animations, loading states, and notifications'\n  }];\n  const achievements = [{\n    metric: '100%',\n    label: 'Responsive Design'\n  }, {\n    metric: '8+',\n    label: 'Key Features'\n  }, {\n    metric: '7',\n    label: 'Technologies Used'\n  }, {\n    metric: '50+',\n    label: 'Components Built'\n  }];\n  const codeSnippets = {\n    frontend: `// Modern React Component with Hooks\nconst Item = ({ id, name, image, new_price, old_price }) => {\n    const [isWishlisted, setIsWishlisted] = useState(false);\n    const { addToCart } = useContext(ShopContext);\n\n    const handleWishlistToggle = async () => {\n        const endpoint = isWishlisted ? 'removefromwishlist' : 'addtowishlist';\n        await fetch(\\`http://localhost:3000/\\${endpoint}\\`, {\n            method: 'POST',\n            headers: {\n                'auth-token': localStorage.getItem('auth-token'),\n                'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({ itemId: id }),\n        });\n        setIsWishlisted(!isWishlisted);\n    };\n\n    return (\n        <div className=\"item\">\n            <img src={image} alt={name} />\n            <button onClick={handleWishlistToggle}>\n                <FaHeart className={isWishlisted ? 'active' : ''} />\n            </button>\n            <h3>{name}</h3>\n            <div className=\"prices\">\n                <span className=\"new-price\">₹{new_price}</span>\n                <span className=\"old-price\">₹{old_price}</span>\n            </div>\n            <button onClick={() => addToCart(id)}>\n                Add to Cart\n            </button>\n        </div>\n    );\n};`,\n    backend: `// Express.js API with MongoDB\napp.post('/addtowishlist', fetchUser, async (req, res) => {\n    try {\n        let userData = await Users.findOne({ _id: req.user.id });\n        if (!userData.wishlist.includes(req.body.itemId)) {\n            userData.wishlist.push(req.body.itemId);\n            await Users.findOneAndUpdate(\n                { _id: req.user.id }, \n                { wishlist: userData.wishlist }\n            );\n        }\n        res.json({ success: true, message: \"Added to wishlist\" });\n    } catch (error) {\n        res.status(500).json({ success: false, errors: \"Server Error\" });\n    }\n});\n\n// Advanced search with filters\napp.get('/search', async (req, res) => {\n    const { query, category, minPrice, maxPrice, sortBy } = req.query;\n    let filter = {};\n    \n    if (query) {\n        filter.$or = [\n            { name: { $regex: query, $options: 'i' } },\n            { description: { $regex: query, $options: 'i' } }\n        ];\n    }\n    \n    if (category && category !== 'all') {\n        filter.category = category;\n    }\n    \n    if (minPrice || maxPrice) {\n        filter.new_price = {};\n        if (minPrice) filter.new_price.$gte = Number(minPrice);\n        if (maxPrice) filter.new_price.$lte = Number(maxPrice);\n    }\n    \n    let products = await Product.find(filter);\n    \n    // Sort products based on criteria\n    if (sortBy === 'price_low') {\n        products.sort((a, b) => a.new_price - b.new_price);\n    }\n    \n    res.send(products);\n});`\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"portfolio\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"portfolio-hero\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"FashionClub E-Commerce Platform\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-subtitle\",\n          children: \"A modern, full-stack e-commerce solution built with React.js and Node.js\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-stats\",\n          children: achievements.map((achievement, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-number\",\n              children: achievement.metric\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"stat-label\",\n              children: achievement.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 33\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 29\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"https://github.com/PARTH/e-Commerce\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            className: \"btn-primary\",\n            children: [/*#__PURE__*/_jsxDEV(FaGithub, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 29\n            }, this), \" View Source Code\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#demo\",\n            className: \"btn-secondary\",\n            children: [/*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 29\n            }, this), \" Live Demo\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-visual\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mockup-browser\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"browser-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"browser-dots\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"browser-url\",\n              children: \"fashionclub.com\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"browser-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mockup-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mockup-header\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mockup-products\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mockup-product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mockup-product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mockup-product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mockup-product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"portfolio-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-navigation\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === 'overview' ? 'active' : ''}`,\n          onClick: () => setActiveTab('overview'),\n          children: \"Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === 'features' ? 'active' : ''}`,\n          onClick: () => setActiveTab('features'),\n          children: \"Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === 'tech' ? 'active' : ''}`,\n          onClick: () => setActiveTab('tech'),\n          children: \"Technology\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `tab-btn ${activeTab === 'code' ? 'active' : ''}`,\n          onClick: () => setActiveTab('code'),\n          children: \"Code Examples\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tab-content\",\n        children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overview-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Project Overview\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overview-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overview-text\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"About This Project\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"FashionClub is a comprehensive e-commerce platform that demonstrates modern web development practices and technologies. Built from the ground up with a focus on user experience, performance, and scalability.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Key Highlights\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Full-stack MERN application with modern architecture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Responsive design that works seamlessly across all devices\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Advanced features like wishlist, search, and user profiles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Secure authentication and authorization system\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Clean, maintainable code with component-based architecture\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Modern UI/UX with dark mode and smooth animations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"overview-metrics\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Development Metrics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metrics-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: \"2 weeks\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Development Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: \"50+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Components\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: \"15+\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"API Endpoints\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"metric-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-value\",\n                    children: \"100%\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"metric-label\",\n                    children: \"Mobile Responsive\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 25\n        }, this), activeTab === 'features' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Key Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"features-grid\",\n            children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"feature-icon\",\n                children: feature.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 25\n        }, this), activeTab === 'tech' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Technology Stack\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-grid\",\n            children: technologies.map((tech, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"tech-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tech-icon\",\n                children: tech.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: tech.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: tech.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 41\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 37\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"architecture-diagram\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"System Architecture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"architecture-flow\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"arch-layer\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Frontend (React.js)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"User Interface, State Management, Routing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 312,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"arch-arrow\",\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"arch-layer\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Backend (Node.js + Express)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"API Endpoints, Authentication, Business Logic\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"arch-arrow\",\n                children: \"\\u2193\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"arch-layer\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"Database (MongoDB)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Data Storage, User Management, Product Catalog\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 25\n        }, this), activeTab === 'code' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"code-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"Code Examples\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"code-examples\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"code-example\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Frontend - React Component\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: codeSnippets.frontend\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"code-example\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Backend - Express.js API\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                children: /*#__PURE__*/_jsxDEV(\"code\", {\n                  children: codeSnippets.backend\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"portfolio-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Ready to Collaborate?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"I'm passionate about creating exceptional web experiences. Let's build something amazing together!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"contact-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"mailto:<EMAIL>\",\n            className: \"btn-primary\",\n            children: \"Get In Touch\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/resume.pdf\",\n            target: \"_blank\",\n            className: \"btn-secondary\",\n            children: \"Download Resume\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 156,\n    columnNumber: 9\n  }, this);\n};\n_s(Portfolio, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = Portfolio;\nexport default Portfolio;\nvar _c;\n$RefreshReg$(_c, \"Portfolio\");", "map": {"version": 3, "names": ["React", "useState", "FaReact", "FaNodeJs", "FaDatabase", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaExternalLinkAlt", "FaCode", "FaMobile", "FaShoppingCart", "FaUsers", "FaSearch", "FaHeart", "FaPalette", "FaShieldAlt", "SiMongodb", "SiExpress", "SiJavascript", "SiCss3", "SiHtml5", "jsxDEV", "_jsxDEV", "Portfolio", "_s", "activeTab", "setActiveTab", "technologies", "name", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "features", "title", "achievements", "metric", "label", "codeSnippets", "frontend", "backend", "className", "children", "map", "achievement", "index", "href", "target", "rel", "onClick", "feature", "tech", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Portfolio/Portfolio.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { FaReact, FaNodeJs, FaDatabase, FaGithub, FaExternalLinkAlt, FaCode, FaMobile, FaShoppingCart, FaUsers, FaSearch, FaHeart, FaPalette, FaShieldAlt } from 'react-icons/fa';\nimport { SiMongodb, SiExpress, SiJavascript, SiCss3, SiHtml5 } from 'react-icons/si';\nimport './Portfolio.css';\n\nconst Portfolio = () => {\n    const [activeTab, setActiveTab] = useState('overview');\n\n    const technologies = [\n        { name: 'React.js', icon: <FaReact />, description: 'Frontend framework for building user interfaces' },\n        { name: 'Node.js', icon: <FaNodeJs />, description: 'Backend runtime environment' },\n        { name: 'Express.js', icon: <SiExpress />, description: 'Web application framework for Node.js' },\n        { name: 'MongoDB', icon: <SiMongodb />, description: 'NoSQL database for data storage' },\n        { name: 'JavaScript', icon: <SiJavascript />, description: 'Programming language for web development' },\n        { name: 'CSS3', icon: <SiCss3 />, description: 'Styling and responsive design' },\n        { name: 'HTML5', icon: <SiHtml5 />, description: 'Markup language for web structure' }\n    ];\n\n    const features = [\n        {\n            icon: <FaShoppingCart />,\n            title: 'Advanced Shopping Cart',\n            description: 'Dynamic cart with quantity management, promo codes, and real-time total calculation'\n        },\n        {\n            icon: <FaHeart />,\n            title: 'Wishlist System',\n            description: 'Save favorite products with persistent storage and easy cart integration'\n        },\n        {\n            icon: <FaSearch />,\n            title: 'Smart Search & Filters',\n            description: 'Advanced product search with category, price, and rating filters'\n        },\n        {\n            icon: <FaUsers />,\n            title: 'User Management',\n            description: 'Complete user authentication, profiles, and order history tracking'\n        },\n        {\n            icon: <FaMobile />,\n            title: 'Responsive Design',\n            description: 'Fully responsive layout optimized for all device sizes'\n        },\n        {\n            icon: <FaPalette />,\n            title: 'Dark Mode',\n            description: 'Toggle between light and dark themes with smooth transitions'\n        },\n        {\n            icon: <FaShieldAlt />,\n            title: 'Secure Authentication',\n            description: 'JWT-based authentication with protected routes and user sessions'\n        },\n        {\n            icon: <FaCode />,\n            title: 'Modern UI/UX',\n            description: 'Contemporary design with animations, loading states, and notifications'\n        }\n    ];\n\n    const achievements = [\n        { metric: '100%', label: 'Responsive Design' },\n        { metric: '8+', label: 'Key Features' },\n        { metric: '7', label: 'Technologies Used' },\n        { metric: '50+', label: 'Components Built' }\n    ];\n\n    const codeSnippets = {\n        frontend: `// Modern React Component with Hooks\nconst Item = ({ id, name, image, new_price, old_price }) => {\n    const [isWishlisted, setIsWishlisted] = useState(false);\n    const { addToCart } = useContext(ShopContext);\n\n    const handleWishlistToggle = async () => {\n        const endpoint = isWishlisted ? 'removefromwishlist' : 'addtowishlist';\n        await fetch(\\`http://localhost:3000/\\${endpoint}\\`, {\n            method: 'POST',\n            headers: {\n                'auth-token': localStorage.getItem('auth-token'),\n                'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({ itemId: id }),\n        });\n        setIsWishlisted(!isWishlisted);\n    };\n\n    return (\n        <div className=\"item\">\n            <img src={image} alt={name} />\n            <button onClick={handleWishlistToggle}>\n                <FaHeart className={isWishlisted ? 'active' : ''} />\n            </button>\n            <h3>{name}</h3>\n            <div className=\"prices\">\n                <span className=\"new-price\">₹{new_price}</span>\n                <span className=\"old-price\">₹{old_price}</span>\n            </div>\n            <button onClick={() => addToCart(id)}>\n                Add to Cart\n            </button>\n        </div>\n    );\n};`,\n        backend: `// Express.js API with MongoDB\napp.post('/addtowishlist', fetchUser, async (req, res) => {\n    try {\n        let userData = await Users.findOne({ _id: req.user.id });\n        if (!userData.wishlist.includes(req.body.itemId)) {\n            userData.wishlist.push(req.body.itemId);\n            await Users.findOneAndUpdate(\n                { _id: req.user.id }, \n                { wishlist: userData.wishlist }\n            );\n        }\n        res.json({ success: true, message: \"Added to wishlist\" });\n    } catch (error) {\n        res.status(500).json({ success: false, errors: \"Server Error\" });\n    }\n});\n\n// Advanced search with filters\napp.get('/search', async (req, res) => {\n    const { query, category, minPrice, maxPrice, sortBy } = req.query;\n    let filter = {};\n    \n    if (query) {\n        filter.$or = [\n            { name: { $regex: query, $options: 'i' } },\n            { description: { $regex: query, $options: 'i' } }\n        ];\n    }\n    \n    if (category && category !== 'all') {\n        filter.category = category;\n    }\n    \n    if (minPrice || maxPrice) {\n        filter.new_price = {};\n        if (minPrice) filter.new_price.$gte = Number(minPrice);\n        if (maxPrice) filter.new_price.$lte = Number(maxPrice);\n    }\n    \n    let products = await Product.find(filter);\n    \n    // Sort products based on criteria\n    if (sortBy === 'price_low') {\n        products.sort((a, b) => a.new_price - b.new_price);\n    }\n    \n    res.send(products);\n});`\n    };\n\n    return (\n        <div className=\"portfolio\">\n            <div className=\"portfolio-hero\">\n                <div className=\"hero-content\">\n                    <h1>FashionClub E-Commerce Platform</h1>\n                    <p className=\"hero-subtitle\">\n                        A modern, full-stack e-commerce solution built with React.js and Node.js\n                    </p>\n                    <div className=\"hero-stats\">\n                        {achievements.map((achievement, index) => (\n                            <div key={index} className=\"stat\">\n                                <div className=\"stat-number\">{achievement.metric}</div>\n                                <div className=\"stat-label\">{achievement.label}</div>\n                            </div>\n                        ))}\n                    </div>\n                    <div className=\"hero-actions\">\n                        <a href=\"https://github.com/PARTH/e-Commerce\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"btn-primary\">\n                            <FaGithub /> View Source Code\n                        </a>\n                        <a href=\"#demo\" className=\"btn-secondary\">\n                            <FaExternalLinkAlt /> Live Demo\n                        </a>\n                    </div>\n                </div>\n                <div className=\"hero-visual\">\n                    <div className=\"mockup-browser\">\n                        <div className=\"browser-header\">\n                            <div className=\"browser-dots\">\n                                <span></span>\n                                <span></span>\n                                <span></span>\n                            </div>\n                            <div className=\"browser-url\">fashionclub.com</div>\n                        </div>\n                        <div className=\"browser-content\">\n                            <div className=\"mockup-content\">\n                                <div className=\"mockup-header\"></div>\n                                <div className=\"mockup-products\">\n                                    <div className=\"mockup-product\"></div>\n                                    <div className=\"mockup-product\"></div>\n                                    <div className=\"mockup-product\"></div>\n                                    <div className=\"mockup-product\"></div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <div className=\"portfolio-content\">\n                <div className=\"tab-navigation\">\n                    <button\n                        className={`tab-btn ${activeTab === 'overview' ? 'active' : ''}`}\n                        onClick={() => setActiveTab('overview')}\n                    >\n                        Overview\n                    </button>\n                    <button\n                        className={`tab-btn ${activeTab === 'features' ? 'active' : ''}`}\n                        onClick={() => setActiveTab('features')}\n                    >\n                        Features\n                    </button>\n                    <button\n                        className={`tab-btn ${activeTab === 'tech' ? 'active' : ''}`}\n                        onClick={() => setActiveTab('tech')}\n                    >\n                        Technology\n                    </button>\n                    <button\n                        className={`tab-btn ${activeTab === 'code' ? 'active' : ''}`}\n                        onClick={() => setActiveTab('code')}\n                    >\n                        Code Examples\n                    </button>\n                </div>\n\n                <div className=\"tab-content\">\n                    {activeTab === 'overview' && (\n                        <div className=\"overview-section\">\n                            <h2>Project Overview</h2>\n                            <div className=\"overview-grid\">\n                                <div className=\"overview-text\">\n                                    <h3>About This Project</h3>\n                                    <p>\n                                        FashionClub is a comprehensive e-commerce platform that demonstrates modern web development\n                                        practices and technologies. Built from the ground up with a focus on user experience,\n                                        performance, and scalability.\n                                    </p>\n                                    <h3>Key Highlights</h3>\n                                    <ul>\n                                        <li>Full-stack MERN application with modern architecture</li>\n                                        <li>Responsive design that works seamlessly across all devices</li>\n                                        <li>Advanced features like wishlist, search, and user profiles</li>\n                                        <li>Secure authentication and authorization system</li>\n                                        <li>Clean, maintainable code with component-based architecture</li>\n                                        <li>Modern UI/UX with dark mode and smooth animations</li>\n                                    </ul>\n                                </div>\n                                <div className=\"overview-metrics\">\n                                    <h3>Development Metrics</h3>\n                                    <div className=\"metrics-grid\">\n                                        <div className=\"metric-item\">\n                                            <span className=\"metric-value\">2 weeks</span>\n                                            <span className=\"metric-label\">Development Time</span>\n                                        </div>\n                                        <div className=\"metric-item\">\n                                            <span className=\"metric-value\">50+</span>\n                                            <span className=\"metric-label\">Components</span>\n                                        </div>\n                                        <div className=\"metric-item\">\n                                            <span className=\"metric-value\">15+</span>\n                                            <span className=\"metric-label\">API Endpoints</span>\n                                        </div>\n                                        <div className=\"metric-item\">\n                                            <span className=\"metric-value\">100%</span>\n                                            <span className=\"metric-label\">Mobile Responsive</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'features' && (\n                        <div className=\"features-section\">\n                            <h2>Key Features</h2>\n                            <div className=\"features-grid\">\n                                {features.map((feature, index) => (\n                                    <div key={index} className=\"feature-card\">\n                                        <div className=\"feature-icon\">{feature.icon}</div>\n                                        <h3>{feature.title}</h3>\n                                        <p>{feature.description}</p>\n                                    </div>\n                                ))}\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'tech' && (\n                        <div className=\"tech-section\">\n                            <h2>Technology Stack</h2>\n                            <div className=\"tech-grid\">\n                                {technologies.map((tech, index) => (\n                                    <div key={index} className=\"tech-card\">\n                                        <div className=\"tech-icon\">{tech.icon}</div>\n                                        <h3>{tech.name}</h3>\n                                        <p>{tech.description}</p>\n                                    </div>\n                                ))}\n                            </div>\n                            <div className=\"architecture-diagram\">\n                                <h3>System Architecture</h3>\n                                <div className=\"architecture-flow\">\n                                    <div className=\"arch-layer\">\n                                        <h4>Frontend (React.js)</h4>\n                                        <p>User Interface, State Management, Routing</p>\n                                    </div>\n                                    <div className=\"arch-arrow\">↓</div>\n                                    <div className=\"arch-layer\">\n                                        <h4>Backend (Node.js + Express)</h4>\n                                        <p>API Endpoints, Authentication, Business Logic</p>\n                                    </div>\n                                    <div className=\"arch-arrow\">↓</div>\n                                    <div className=\"arch-layer\">\n                                        <h4>Database (MongoDB)</h4>\n                                        <p>Data Storage, User Management, Product Catalog</p>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n\n                    {activeTab === 'code' && (\n                        <div className=\"code-section\">\n                            <h2>Code Examples</h2>\n                            <div className=\"code-examples\">\n                                <div className=\"code-example\">\n                                    <h3>Frontend - React Component</h3>\n                                    <pre><code>{codeSnippets.frontend}</code></pre>\n                                </div>\n                                <div className=\"code-example\">\n                                    <h3>Backend - Express.js API</h3>\n                                    <pre><code>{codeSnippets.backend}</code></pre>\n                                </div>\n                            </div>\n                        </div>\n                    )}\n                </div>\n            </div>\n\n            <div className=\"portfolio-footer\">\n                <div className=\"footer-content\">\n                    <h2>Ready to Collaborate?</h2>\n                    <p>I'm passionate about creating exceptional web experiences. Let's build something amazing together!</p>\n                    <div className=\"contact-actions\">\n                        <a href=\"mailto:<EMAIL>\" className=\"btn-primary\">Get In Touch</a>\n                        <a href=\"/resume.pdf\" target=\"_blank\" className=\"btn-secondary\">Download Resume</a>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n};\n\nexport default Portfolio;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AACjL,SAASC,SAAS,EAAEC,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,QAAQ,gBAAgB;AACpF,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,UAAU,CAAC;EAEtD,MAAMyB,YAAY,GAAG,CACjB;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAEP,OAAA,CAACnB,OAAO;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAAkD,CAAC,EACvG;IAAEN,IAAI,EAAE,SAAS;IAAEC,IAAI,eAAEP,OAAA,CAAClB,QAAQ;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAA8B,CAAC,EACnF;IAAEN,IAAI,EAAE,YAAY;IAAEC,IAAI,eAAEP,OAAA,CAACL,SAAS;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAAwC,CAAC,EACjG;IAAEN,IAAI,EAAE,SAAS;IAAEC,IAAI,eAAEP,OAAA,CAACN,SAAS;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAAkC,CAAC,EACxF;IAAEN,IAAI,EAAE,YAAY;IAAEC,IAAI,eAAEP,OAAA,CAACJ,YAAY;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAA2C,CAAC,EACvG;IAAEN,IAAI,EAAE,MAAM;IAAEC,IAAI,eAAEP,OAAA,CAACH,MAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAAgC,CAAC,EAChF;IAAEN,IAAI,EAAE,OAAO;IAAEC,IAAI,eAAEP,OAAA,CAACF,OAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,WAAW,EAAE;EAAoC,CAAC,CACzF;EAED,MAAMC,QAAQ,GAAG,CACb;IACIN,IAAI,eAAEP,OAAA,CAACZ,cAAc;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBG,KAAK,EAAE,wBAAwB;IAC/BF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACT,OAAO;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBG,KAAK,EAAE,iBAAiB;IACxBF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACV,QAAQ;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBG,KAAK,EAAE,wBAAwB;IAC/BF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACX,OAAO;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBG,KAAK,EAAE,iBAAiB;IACxBF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACb,QAAQ;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBG,KAAK,EAAE,mBAAmB;IAC1BF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACR,SAAS;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBG,KAAK,EAAE,WAAW;IAClBF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACP,WAAW;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBG,KAAK,EAAE,uBAAuB;IAC9BF,WAAW,EAAE;EACjB,CAAC,EACD;IACIL,IAAI,eAAEP,OAAA,CAACd,MAAM;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAChBG,KAAK,EAAE,cAAc;IACrBF,WAAW,EAAE;EACjB,CAAC,CACJ;EAED,MAAMG,YAAY,GAAG,CACjB;IAAEC,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAC9C;IAAED,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAe,CAAC,EACvC;IAAED,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAC3C;IAAED,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAmB,CAAC,CAC/C;EAED,MAAMC,YAAY,GAAG;IACjBC,QAAQ,EAAG;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;IACKC,OAAO,EAAG;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,CAAC;EAED,oBACIpB,OAAA;IAAKqB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACtBtB,OAAA;MAAKqB,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC3BtB,OAAA;QAAKqB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBACzBtB,OAAA;UAAAsB,QAAA,EAAI;QAA+B;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxCX,OAAA;UAAGqB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJX,OAAA;UAAKqB,SAAS,EAAC,YAAY;UAAAC,QAAA,EACtBP,YAAY,CAACQ,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACjCzB,OAAA;YAAiBqB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAC7BtB,OAAA;cAAKqB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEE,WAAW,CAACR;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvDX,OAAA;cAAKqB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEE,WAAW,CAACP;YAAK;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAF/Cc,KAAK;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGV,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNX,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtB,OAAA;YAAG0B,IAAI,EAAC,qCAAqC;YAACC,MAAM,EAAC,QAAQ;YAACC,GAAG,EAAC,qBAAqB;YAACP,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC3GtB,OAAA,CAAChB,QAAQ;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJX,OAAA;YAAG0B,IAAI,EAAC,OAAO;YAACL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBACrCtB,OAAA,CAACf,iBAAiB;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cACzB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNX,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,eACxBtB,OAAA;UAAKqB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC3BtB,OAAA;YAAKqB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC3BtB,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtB,OAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbX,OAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACbX,OAAA;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACNX,OAAA;cAAKqB,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAe;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC,eACNX,OAAA;YAAKqB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC5BtB,OAAA;cAAKqB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC3BtB,OAAA;gBAAKqB,SAAS,EAAC;cAAe;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrCX,OAAA;gBAAKqB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC5BtB,OAAA;kBAAKqB,SAAS,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCX,OAAA;kBAAKqB,SAAS,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCX,OAAA;kBAAKqB,SAAS,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtCX,OAAA;kBAAKqB,SAAS,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENX,OAAA;MAAKqB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAC9BtB,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BtB,OAAA;UACIqB,SAAS,EAAG,WAAUlB,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAE;UACjE0B,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,UAAU,CAAE;UAAAkB,QAAA,EAC3C;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA;UACIqB,SAAS,EAAG,WAAUlB,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAG,EAAE;UACjE0B,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,UAAU,CAAE;UAAAkB,QAAA,EAC3C;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA;UACIqB,SAAS,EAAG,WAAUlB,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC7D0B,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,MAAM,CAAE;UAAAkB,QAAA,EACvC;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA;UACIqB,SAAS,EAAG,WAAUlB,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,EAAG,EAAE;UAC7D0B,OAAO,EAAEA,CAAA,KAAMzB,YAAY,CAAC,MAAM,CAAE;UAAAkB,QAAA,EACvC;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAENX,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,GACvBnB,SAAS,KAAK,UAAU,iBACrBH,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7BtB,OAAA;YAAAsB,QAAA,EAAI;UAAgB;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBX,OAAA;YAAKqB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BtB,OAAA;cAAKqB,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BtB,OAAA;gBAAAsB,QAAA,EAAI;cAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BX,OAAA;gBAAAsB,QAAA,EAAG;cAIH;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJX,OAAA;gBAAAsB,QAAA,EAAI;cAAc;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBX,OAAA;gBAAAsB,QAAA,gBACItB,OAAA;kBAAAsB,QAAA,EAAI;gBAAoD;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7DX,OAAA;kBAAAsB,QAAA,EAAI;gBAA0D;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEX,OAAA;kBAAAsB,QAAA,EAAI;gBAA0D;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEX,OAAA;kBAAAsB,QAAA,EAAI;gBAA8C;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvDX,OAAA;kBAAAsB,QAAA,EAAI;gBAA0D;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEX,OAAA;kBAAAsB,QAAA,EAAI;gBAAiD;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNX,OAAA;cAAKqB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC7BtB,OAAA;gBAAAsB,QAAA,EAAI;cAAmB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5BX,OAAA;gBAAKqB,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBtB,OAAA;kBAAKqB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBtB,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAO;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7CX,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrD,CAAC,eACNX,OAAA;kBAAKqB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBtB,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCX,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAU;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC,eACNX,OAAA;kBAAKqB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBtB,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAG;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCX,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAa;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNX,OAAA;kBAAKqB,SAAS,EAAC,aAAa;kBAAAC,QAAA,gBACxBtB,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAI;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1CX,OAAA;oBAAMqB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAiB;oBAAAd,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEAR,SAAS,KAAK,UAAU,iBACrBH,OAAA;UAAKqB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC7BtB,OAAA;YAAAsB,QAAA,EAAI;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBX,OAAA;YAAKqB,SAAS,EAAC,eAAe;YAAAC,QAAA,EACzBT,QAAQ,CAACU,GAAG,CAAC,CAACO,OAAO,EAAEL,KAAK,kBACzBzB,OAAA;cAAiBqB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACrCtB,OAAA;gBAAKqB,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEQ,OAAO,CAACvB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAClDX,OAAA;gBAAAsB,QAAA,EAAKQ,OAAO,CAAChB;cAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxBX,OAAA;gBAAAsB,QAAA,EAAIQ,OAAO,CAAClB;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHtBc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEAR,SAAS,KAAK,MAAM,iBACjBH,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtB,OAAA;YAAAsB,QAAA,EAAI;UAAgB;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBX,OAAA;YAAKqB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACrBjB,YAAY,CAACkB,GAAG,CAAC,CAACQ,IAAI,EAAEN,KAAK,kBAC1BzB,OAAA;cAAiBqB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAClCtB,OAAA;gBAAKqB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAES,IAAI,CAACxB;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CX,OAAA;gBAAAsB,QAAA,EAAKS,IAAI,CAACzB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBX,OAAA;gBAAAsB,QAAA,EAAIS,IAAI,CAACnB;cAAW;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA,GAHnBc,KAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIV,CACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNX,OAAA;YAAKqB,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACjCtB,OAAA;cAAAsB,QAAA,EAAI;YAAmB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5BX,OAAA;cAAKqB,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BtB,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBtB,OAAA;kBAAAsB,QAAA,EAAI;gBAAmB;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5BX,OAAA;kBAAAsB,QAAA,EAAG;gBAAyC;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACNX,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCX,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBtB,OAAA;kBAAAsB,QAAA,EAAI;gBAA2B;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpCX,OAAA;kBAAAsB,QAAA,EAAG;gBAA6C;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNX,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAC;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACnCX,OAAA;gBAAKqB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACvBtB,OAAA;kBAAAsB,QAAA,EAAI;gBAAkB;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3BX,OAAA;kBAAAsB,QAAA,EAAG;gBAA8C;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,EAEAR,SAAS,KAAK,MAAM,iBACjBH,OAAA;UAAKqB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBACzBtB,OAAA;YAAAsB,QAAA,EAAI;UAAa;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtBX,OAAA;YAAKqB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC1BtB,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtB,OAAA;gBAAAsB,QAAA,EAAI;cAA0B;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCX,OAAA;gBAAAsB,QAAA,eAAKtB,OAAA;kBAAAsB,QAAA,EAAOJ,YAAY,CAACC;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACNX,OAAA;cAAKqB,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBtB,OAAA;gBAAAsB,QAAA,EAAI;cAAwB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCX,OAAA;gBAAAsB,QAAA,eAAKtB,OAAA;kBAAAsB,QAAA,EAAOJ,YAAY,CAACE;gBAAO;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENX,OAAA;MAAKqB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC7BtB,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC3BtB,OAAA;UAAAsB,QAAA,EAAI;QAAqB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC9BX,OAAA;UAAAsB,QAAA,EAAG;QAAkG;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACzGX,OAAA;UAAKqB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC5BtB,OAAA;YAAG0B,IAAI,EAAC,+BAA+B;YAACL,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFX,OAAA;YAAG0B,IAAI,EAAC,aAAa;YAACC,MAAM,EAAC,QAAQ;YAACN,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAe;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACT,EAAA,CAjWID,SAAS;AAAA+B,EAAA,GAAT/B,SAAS;AAmWf,eAAeA,SAAS;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}