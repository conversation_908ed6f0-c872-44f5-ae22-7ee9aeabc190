{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Pages\\\\Cart.jsx\";\nimport React from 'react';\nimport CartItems from '../Components/CartItems/CartItems';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Cart = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(CartItems, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 9\n  }, this);\n};\n_c = Cart;\nexport default Cart;\nvar _c;\n$RefreshReg$(_c, \"Cart\");", "map": {"version": 3, "names": ["React", "CartItems", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Pages/Cart.jsx"], "sourcesContent": ["import React from 'react'\r\nimport CartItems from '../Components/CartItems/CartItems'\r\n\r\nconst Cart = () => {\r\n    return (\r\n        <div>\r\n            <CartItems />\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default Cart\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,mCAAmC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,IAAI,GAAGA,CAAA,KAAM;EACf,oBACID,OAAA;IAAAE,QAAA,eACIF,OAAA,CAACF,SAAS;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEZ,CAAC;AAEd,CAAC;AAAAC,EAAA,GAPKN,IAAI;AASV,eAAeA,IAAI;AAAA,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}