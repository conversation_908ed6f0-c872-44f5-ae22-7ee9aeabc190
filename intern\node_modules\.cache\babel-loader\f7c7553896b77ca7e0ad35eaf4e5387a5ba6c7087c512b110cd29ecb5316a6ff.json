{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\NewCollection\\\\NewCollection.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './NewCollection.css';\nimport Item from '../Item/Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NewCollection = () => {\n  _s();\n  const [new_collection, setNew_collection] = useState([]);\n  useEffect(() => {\n    fetch('http://localhost:3000/newcollections').then(response => response.json()).then(data => setNew_collection(data));\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"new-Collection\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"NEW COLLECTIONS\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"collection\",\n      children: new_collection.map((item, i) => {\n        return /*#__PURE__*/_jsxDEV(Item, {\n          id: item.id,\n          name: item.name,\n          image: item.image,\n          new_price: item.new_price,\n          old_price: item.old_price\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 28\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 9\n  }, this);\n};\n_s(NewCollection, \"VfajDx86lqJF+guSFbjJ5ChSAoc=\");\n_c = NewCollection;\nexport default NewCollection;\nvar _c;\n$RefreshReg$(_c, \"NewCollection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "NewCollection", "_s", "new_collection", "setNew_collection", "fetch", "then", "response", "json", "data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "i", "id", "name", "image", "new_price", "old_price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/NewCollection/NewCollection.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './NewCollection.css';\r\nimport Item from '../Item/Item';\r\n\r\nconst NewCollection = () => {\r\n    const [new_collection, setNew_collection] = useState([]);\r\n\r\n    useEffect(() => {\r\n        fetch('http://localhost:3000/newcollections')\r\n            .then((response) => response.json())\r\n            .then((data) => setNew_collection(data));\r\n    }, []);\r\n\r\n    return (\r\n        <div className=\"new-Collection\">\r\n            <h1>NEW COLLECTIONS</h1>\r\n            <hr />\r\n            <div className=\"collection\">\r\n                {new_collection.map((item, i) => {\r\n                    return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price} />\r\n                })}\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default NewCollection;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,qBAAqB;AAC5B,OAAOC,IAAI,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAExDC,SAAS,CAAC,MAAM;IACZQ,KAAK,CAAC,sCAAsC,CAAC,CACxCC,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAKL,iBAAiB,CAACK,IAAI,CAAC,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIT,OAAA;IAAKU,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC3BX,OAAA;MAAAW,QAAA,EAAI;IAAe;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACxBf,OAAA;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNf,OAAA;MAAKU,SAAS,EAAC,YAAY;MAAAC,QAAA,EACtBR,cAAc,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;QAC7B,oBAAOlB,OAAA,CAACF,IAAI;UAASqB,EAAE,EAAEF,IAAI,CAACE,EAAG;UAACC,IAAI,EAAEH,IAAI,CAACG,IAAK;UAACC,KAAK,EAAEJ,IAAI,CAACI,KAAM;UAACC,SAAS,EAAEL,IAAI,CAACK,SAAU;UAACC,SAAS,EAAEN,IAAI,CAACM;QAAU,GAAzGL,CAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0G,CAAC;MAClI,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACb,EAAA,CApBID,aAAa;AAAAuB,EAAA,GAAbvB,aAAa;AAsBnB,eAAeA,aAAa;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}