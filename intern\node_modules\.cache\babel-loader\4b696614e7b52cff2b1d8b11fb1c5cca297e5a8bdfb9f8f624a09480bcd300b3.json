{"ast": null, "code": "import p1_img from './product_1.png';\nimport p2_img from './product_2.png';\nimport p3_img from './product_3.png';\nimport p4_img from './product_4.png';\nlet data_product = [{\n  id: 1,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p1_img,\n  new_price: 50.00,\n  old_price: 80.50\n}, {\n  id: 2,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p2_img,\n  new_price: 85.00,\n  old_price: 120.50\n}, {\n  id: 3,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p3_img,\n  new_price: 60.00,\n  old_price: 100.50\n}, {\n  id: 4,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p4_img,\n  new_price: 100.00,\n  old_price: 150.00\n}];\nexport default data_product;", "map": {"version": 3, "names": ["p1_img", "p2_img", "p3_img", "p4_img", "data_product", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Asset/data.js"], "sourcesContent": ["import p1_img from './product_1.png'\r\nimport p2_img from './product_2.png'\r\nimport p3_img from './product_3.png'\r\nimport p4_img from './product_4.png'\r\n\r\nlet data_product = [\r\n  {\r\n    id:1,\r\n    name:\"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image:p1_img,\r\n    new_price:50.00,\r\n    old_price:80.50,\r\n  },\r\n  {id:2,\r\n    name:\"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image:p2_img,\r\n    new_price:85.00,\r\n    old_price:120.50,\r\n  },\r\n  {id:3,\r\n    name:\"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image:p3_img,\r\n    new_price:60.00,\r\n    old_price:100.50,\r\n  },\r\n  {id:4,\r\n    name:\"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image:p4_img,\r\n    new_price:100.00,\r\n    old_price:150.00,\r\n  },\r\n];\r\n\r\nexport default data_product;\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,iBAAiB;AAEpC,IAAIC,YAAY,GAAG,CACjB;EACEC,EAAE,EAAC,CAAC;EACJC,IAAI,EAAC,yDAAyD;EAC9DC,KAAK,EAACP,MAAM;EACZQ,SAAS,EAAC,KAAK;EACfC,SAAS,EAAC;AACZ,CAAC,EACD;EAACJ,EAAE,EAAC,CAAC;EACHC,IAAI,EAAC,yDAAyD;EAC9DC,KAAK,EAACN,MAAM;EACZO,SAAS,EAAC,KAAK;EACfC,SAAS,EAAC;AACZ,CAAC,EACD;EAACJ,EAAE,EAAC,CAAC;EACHC,IAAI,EAAC,yDAAyD;EAC9DC,KAAK,EAACL,MAAM;EACZM,SAAS,EAAC,KAAK;EACfC,SAAS,EAAC;AACZ,CAAC,EACD;EAACJ,EAAE,EAAC,CAAC;EACHC,IAAI,EAAC,yDAAyD;EAC9DC,KAAK,EAACJ,MAAM;EACZK,SAAS,EAAC,MAAM;EAChBC,SAAS,EAAC;AACZ,CAAC,CACF;AAED,eAAeL,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}