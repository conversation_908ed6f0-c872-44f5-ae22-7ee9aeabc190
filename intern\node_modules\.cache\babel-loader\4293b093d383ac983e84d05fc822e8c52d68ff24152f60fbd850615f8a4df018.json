{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Offers\\\\Offers.jsx\";\nimport React from 'react';\nimport './Offers.css';\nimport exclusive_image from '../Asset/exclusive_image.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Offers = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"offers\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"offers-left\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Exclusive\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Offer For You\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"ONLY ON BEST SELLERS PRODUCTS\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        children: \"Check Now\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"offers-right\",\n      children: /*#__PURE__*/_jsxDEV(\"img\", {\n        src: exclusive_image,\n        alt: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 9\n  }, this);\n};\n_c = Offers;\nexport default Offers;\nvar _c;\n$RefreshReg$(_c, \"Offers\");", "map": {"version": 3, "names": ["React", "exclusive_image", "jsxDEV", "_jsxDEV", "Offers", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Offers/Offers.jsx"], "sourcesContent": ["import React from 'react';\r\nimport './Offers.css';\r\nimport exclusive_image from '../Asset/exclusive_image.png'\r\n\r\nconst Offers = () => {\r\n\r\n\r\n    return (\r\n        <div className=\"offers\">\r\n            <div className=\"offers-left\">\r\n                <h1>Exclusive</h1>\r\n                <h1>Offer For You</h1>\r\n                <p>ONLY ON BEST SELLERS PRODUCTS</p>\r\n                <button>Check Now</button>\r\n\r\n            </div>\r\n            <div className=\"offers-right\">\r\n                <img src={exclusive_image} alt=\"\" />\r\n\r\n            </div>\r\n\r\n\r\n        </div>\r\n\r\n    );\r\n}\r\n\r\nexport default Offers;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,cAAc;AACrB,OAAOC,eAAe,MAAM,8BAA8B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAGjB,oBACID,OAAA;IAAKE,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACnBH,OAAA;MAAKE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBH,OAAA;QAAAG,QAAA,EAAI;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClBP,OAAA;QAAAG,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtBP,OAAA;QAAAG,QAAA,EAAG;MAA6B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpCP,OAAA;QAAAG,QAAA,EAAQ;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEzB,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,cAAc;MAAAC,QAAA,eACzBH,OAAA;QAAKQ,GAAG,EAAEV,eAAgB;QAACW,GAAG,EAAC;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGL,CAAC;AAGd,CAAC;AAAAG,EAAA,GArBKT,MAAM;AAuBZ,eAAeA,MAAM;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}