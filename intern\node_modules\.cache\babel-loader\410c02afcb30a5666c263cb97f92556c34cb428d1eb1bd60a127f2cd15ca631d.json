{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\Breadcrums\\\\Breadcrums.jsx\";\nimport React from 'react';\nimport './Breadcrums.css';\nimport arrow_icon from '../Asset/breadcrum_arrow.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Breadcrums = props => {\n  const {\n    product\n  } = props;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"breadcrums\",\n    children: [\"HOME \", /*#__PURE__*/_jsxDEV(\"img\", {\n      src: arrow_icon,\n      alt: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 18\n    }, this), \"SHOP \", /*#__PURE__*/_jsxDEV(\"img\", {\n      src: arrow_icon,\n      alt: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 54\n    }, this), \" \", product.category, \" \", /*#__PURE__*/_jsxDEV(\"img\", {\n      src: arrow_icon,\n      alt: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 105\n    }, this), product.name]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 9\n  }, this);\n};\n_c = Breadcrums;\nexport default Breadcrums;\nvar _c;\n$RefreshReg$(_c, \"Breadcrums\");", "map": {"version": 3, "names": ["React", "arrow_icon", "jsxDEV", "_jsxDEV", "Breadcrums", "props", "product", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "category", "name", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Breadcrums/Breadcrums.jsx"], "sourcesContent": ["import React from 'react';\r\nimport './Breadcrums.css'\r\nimport arrow_icon from '../Asset/breadcrum_arrow.png'\r\n\r\nconst Breadcrums = (props) => {\r\n    const { product } = props;\r\n    return (\r\n        <div className=\"breadcrums\">\r\n            HOME <img src={arrow_icon} alt=\"\" />SHOP <img src={arrow_icon} alt=\"\" /> {product.category} <img src={arrow_icon} alt=\"\" />{product.name}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Breadcrums;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,kBAAkB;AACzB,OAAOC,UAAU,MAAM,8BAA8B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,UAAU,GAAIC,KAAK,IAAK;EAC1B,MAAM;IAAEC;EAAQ,CAAC,GAAGD,KAAK;EACzB,oBACIF,OAAA;IAAKI,SAAS,EAAC,YAAY;IAAAC,QAAA,GAAC,OACnB,eAAAL,OAAA;MAAKM,GAAG,EAAER,UAAW;MAACS,GAAG,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,SAAK,eAAAX,OAAA;MAAKM,GAAG,EAAER,UAAW;MAACS,GAAG,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,KAAC,EAACR,OAAO,CAACS,QAAQ,EAAC,GAAC,eAAAZ,OAAA;MAAKM,GAAG,EAAER,UAAW;MAACS,GAAG,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAACR,OAAO,CAACU,IAAI;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvI,CAAC;AAEd,CAAC;AAAAG,EAAA,GAPKb,UAAU;AAShB,eAAeA,UAAU;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}