{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Components\\\\RelatedProducts\\\\RelatedProducts.jsx\";\nimport React from 'react';\nimport './RelatedProducts.css';\nimport data_product from '../Asset/data';\nimport Item from '../Item/Item';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RelatedProducts = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relatedproducts\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Related Products\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relatedProducts-item\",\n      children: data_product.map((item, i) => {\n        return /*#__PURE__*/_jsxDEV(Item, {\n          id: item.id,\n          name: item.name,\n          image: item.image,\n          new_price: item.new_price,\n          old_price: item.old_price\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 28\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 9\n  }, this);\n};\n_c = RelatedProducts;\nexport default RelatedProducts;\nvar _c;\n$RefreshReg$(_c, \"RelatedProducts\");", "map": {"version": 3, "names": ["React", "data_product", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "RelatedProducts", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "i", "id", "name", "image", "new_price", "old_price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/RelatedProducts/RelatedProducts.jsx"], "sourcesContent": ["import React from 'react'\r\nimport './RelatedProducts.css'\r\nimport data_product from '../Asset/data'\r\nimport Item from '../Item/Item'\r\nconst RelatedProducts = () => {\r\n    return (\r\n        <div className=\"relatedproducts\">\r\n            <h1>Related Products</h1>\r\n            <hr />\r\n            <div className=\"relatedProducts-item\">\r\n                {data_product.map((item, i) => {\r\n                    return <Item key={i} id={item.id} name={item.name} image={item.image} new_price={item.new_price} old_price={item.old_price} />\r\n                })}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default RelatedProducts\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,uBAAuB;AAC9B,OAAOC,YAAY,MAAM,eAAe;AACxC,OAAOC,IAAI,MAAM,cAAc;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAC/B,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAC1B,oBACID,OAAA;IAAKE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC5BH,OAAA;MAAAG,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBP,OAAA;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNP,OAAA;MAAKE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAChCN,YAAY,CAACW,GAAG,CAAC,CAACC,IAAI,EAAEC,CAAC,KAAK;QAC3B,oBAAOV,OAAA,CAACF,IAAI;UAASa,EAAE,EAAEF,IAAI,CAACE,EAAG;UAACC,IAAI,EAAEH,IAAI,CAACG,IAAK;UAACC,KAAK,EAAEJ,IAAI,CAACI,KAAM;UAACC,SAAS,EAAEL,IAAI,CAACK,SAAU;UAACC,SAAS,EAAEN,IAAI,CAACM;QAAU,GAAzGL,CAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA0G,CAAC;MAClI,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAAS,EAAA,GAZKf,eAAe;AAcrB,eAAeA,eAAe;AAAA,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}