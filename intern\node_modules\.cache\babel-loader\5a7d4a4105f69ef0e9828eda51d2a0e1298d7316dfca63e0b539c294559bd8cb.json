{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Context\\\\ShopContext.jsx\",\n  _s = $RefreshSig$();\nimport React, { createContext, useEffect, useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ShopContext = /*#__PURE__*/createContext(null);\nconst getDefaultCart = () => {\n  let cart = {};\n  for (let index = 0; index < 300 + 1; index++) {\n    cart[index] = 0;\n  }\n  return cart;\n};\nconst ShopContextProvider = props => {\n  _s();\n  const [all_product, setAll_product] = useState([]);\n  const [cartItems, setCartItems] = useState(getDefaultCart());\n  useEffect(() => {\n    fetch(process.env.REACT_APP_API_URL || 'http://localhost:3000' + '/allproducts').then(response => response.json()).then(data => {\n      console.log(\"Fetched products:\", data);\n      setAll_product(data);\n    }).catch(error => {\n      console.error(\"Error fetching products:\", error);\n    });\n    if (localStorage.getItem('auth-token')) {\n      fetch('http://localhost:3000/getcart', {\n        method: 'POST',\n        headers: {\n          Accept: 'application/form-data',\n          'auth-token': `${localStorage.getItem('auth-token')}`,\n          'Content-Type': 'application/json'\n        },\n        body: \"\"\n      }).then(response => response.json()).then(data => setCartItems(data));\n    }\n  }, []);\n  const addToCart = itemId => {\n    setCartItems(prev => ({\n      ...prev,\n      [itemId]: prev[itemId] + 1\n    }));\n    if (localStorage.getItem(\"auth-token\")) {\n      fetch(\"http://localhost:3000/addtocart\", {\n        method: \"POST\",\n        headers: {\n          Accept: \"application/json\",\n          \"auth-token\": `${localStorage.getItem(\"auth-token\")}`,\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          itemId: itemId\n        })\n      }).then(response => response.json()).then(data => console.log(data));\n    }\n  };\n  const removeFromCart = itemId => {\n    setCartItems(prev => ({\n      ...prev,\n      [itemId]: prev[itemId] - 1\n    }));\n    if (localStorage.getItem(\"auth-token\")) {\n      fetch(\"http://localhost:3000/removefromcart\", {\n        method: \"POST\",\n        headers: {\n          Accept: \"application/json\",\n          \"auth-token\": `${localStorage.getItem(\"auth-token\")}`,\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          itemId: itemId\n        })\n      }).then(response => response.json()).then(data => console.log(data));\n    }\n  };\n  const getTotalCartAmount = () => {\n    let totalAmount = 0;\n    for (const item in cartItems) {\n      if (cartItems[item] > 0) {\n        let itemInfo = all_product.find(product => product.id === Number(item));\n        if (itemInfo) {\n          totalAmount += itemInfo.new_price * cartItems[item];\n        }\n      }\n    }\n    return totalAmount;\n  };\n  const getTotalCartItems = () => {\n    let totalItem = 0;\n    for (const item in cartItems) {\n      if (cartItems[item] > 0) {\n        totalItem += cartItems[item];\n      }\n    }\n    return totalItem;\n  };\n  const contextValue = {\n    getTotalCartItems,\n    getTotalCartAmount,\n    all_product,\n    cartItems,\n    addToCart,\n    removeFromCart\n  };\n  return /*#__PURE__*/_jsxDEV(ShopContext.Provider, {\n    value: contextValue,\n    children: props.children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 110,\n    columnNumber: 9\n  }, this);\n};\n_s(ShopContextProvider, \"jbBoek488p8ZBu6I+yaEuxzV5/M=\");\n_c = ShopContextProvider;\nexport default ShopContextProvider;\nvar _c;\n$RefreshReg$(_c, \"ShopContextProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useEffect", "useState", "jsxDEV", "_jsxDEV", "ShopContext", "getDefaultCart", "cart", "index", "ShopContextProvider", "props", "_s", "all_product", "setAll_product", "cartItems", "setCartItems", "fetch", "process", "env", "REACT_APP_API_URL", "then", "response", "json", "data", "console", "log", "catch", "error", "localStorage", "getItem", "method", "headers", "Accept", "body", "addToCart", "itemId", "prev", "JSON", "stringify", "removeFromCart", "getTotalCartAmount", "totalAmount", "item", "itemInfo", "find", "product", "id", "Number", "new_price", "getTotalCartItems", "totalItem", "contextValue", "Provider", "value", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Context/ShopContext.jsx"], "sourcesContent": ["import React, { createContext, useEffect, useState } from \"react\";\r\n\r\nexport const ShopContext = createContext(null);\r\n\r\nconst getDefaultCart = () => {\r\n    let cart = {};\r\n    for (let index = 0; index < 300 + 1; index++) {\r\n        cart[index] = 0;\r\n    }\r\n    return cart;\r\n};\r\n\r\nconst ShopContextProvider = (props) => {\r\n    const [all_product, setAll_product] = useState([]);\r\n    const [cartItems, setCartItems] = useState(getDefaultCart());\r\n\r\n    useEffect(() => {\r\n        fetch(process.env.REACT_APP_API_URL || 'http://localhost:3000' + '/allproducts')\r\n            .then((response) => response.json())\r\n            .then((data) => {\r\n                console.log(\"Fetched products:\", data);\r\n                setAll_product(data);\r\n            })\r\n            .catch((error) => {\r\n                console.error(\"Error fetching products:\", error);\r\n            });\r\n\r\n        if (localStorage.getItem('auth-token')) {\r\n            fetch('http://localhost:3000/getcart', {\r\n                method: 'POST',\r\n                headers: {\r\n                    Accept: 'application/form-data',\r\n                    'auth-token': `${localStorage.getItem('auth-token')}`,\r\n                    'Content-Type': 'application/json',\r\n                },\r\n                body: \"\",\r\n            }).then((response) => response.json())\r\n                .then((data) => setCartItems(data));\r\n        }\r\n    }, [])\r\n\r\n    const addToCart = (itemId) => {\r\n        setCartItems((prev) => ({ ...prev, [itemId]: prev[itemId] + 1 }));\r\n\r\n        if (localStorage.getItem(\"auth-token\")) {\r\n            fetch(\"http://localhost:3000/addtocart\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                    Accept: \"application/json\",\r\n                    \"auth-token\": `${localStorage.getItem(\"auth-token\")}`,\r\n                    \"Content-Type\": \"application/json\",\r\n                },\r\n                body: JSON.stringify({ itemId: itemId }),\r\n            })\r\n                .then((response) => response.json())\r\n                .then((data) => console.log(data));\r\n        }\r\n    };\r\n\r\n    const removeFromCart = (itemId) => {\r\n        setCartItems((prev) => ({ ...prev, [itemId]: prev[itemId] - 1 }));\r\n        if (localStorage.getItem(\"auth-token\")) {\r\n            fetch(\"http://localhost:3000/removefromcart\", {\r\n                method: \"POST\",\r\n                headers: {\r\n                    Accept: \"application/json\",\r\n                    \"auth-token\": `${localStorage.getItem(\"auth-token\")}`,\r\n                    \"Content-Type\": \"application/json\",\r\n                },\r\n                body: JSON.stringify({ itemId: itemId }),\r\n            })\r\n                .then((response) => response.json())\r\n                .then((data) => console.log(data));\r\n        }\r\n    };\r\n\r\n    const getTotalCartAmount = () => {\r\n        let totalAmount = 0;\r\n        for (const item in cartItems) {\r\n            if (cartItems[item] > 0) {\r\n                let itemInfo = all_product.find((product) => product.id === Number(item));\r\n                if (itemInfo) {\r\n                    totalAmount += itemInfo.new_price * cartItems[item];\r\n                }\r\n            }\r\n        }\r\n        return totalAmount;\r\n    };\r\n\r\n    const getTotalCartItems = () => {\r\n        let totalItem = 0;\r\n        for (const item in cartItems) {\r\n            if (cartItems[item] > 0) {\r\n                totalItem += cartItems[item];\r\n            }\r\n        }\r\n        return totalItem;\r\n    };\r\n\r\n    const contextValue = {\r\n        getTotalCartItems,\r\n        getTotalCartAmount,\r\n        all_product,\r\n        cartItems,\r\n        addToCart,\r\n        removeFromCart,\r\n    };\r\n\r\n    return (\r\n        <ShopContext.Provider value={contextValue}>\r\n            {props.children}\r\n        </ShopContext.Provider>\r\n    );\r\n};\r\n\r\nexport default ShopContextProvider;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,OAAO,MAAMC,WAAW,gBAAGL,aAAa,CAAC,IAAI,CAAC;AAE9C,MAAMM,cAAc,GAAGA,CAAA,KAAM;EACzB,IAAIC,IAAI,GAAG,CAAC,CAAC;EACb,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,GAAG,GAAG,CAAC,EAAEA,KAAK,EAAE,EAAE;IAC1CD,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC;EACnB;EACA,OAAOD,IAAI;AACf,CAAC;AAED,MAAME,mBAAmB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EACnC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAACI,cAAc,CAAC,CAAC,CAAC;EAE5DL,SAAS,CAAC,MAAM;IACZe,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAG,cAAc,CAAC,CAC3EC,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAK;MACZC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,IAAI,CAAC;MACtCV,cAAc,CAACU,IAAI,CAAC;IACxB,CAAC,CAAC,CACDG,KAAK,CAAEC,KAAK,IAAK;MACdH,OAAO,CAACG,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IACpD,CAAC,CAAC;IAEN,IAAIC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACpCb,KAAK,CAAC,+BAA+B,EAAE;QACnCc,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACLC,MAAM,EAAE,uBAAuB;UAC/B,YAAY,EAAG,GAAEJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,EAAC;UACrD,cAAc,EAAE;QACpB,CAAC;QACDI,IAAI,EAAE;MACV,CAAC,CAAC,CAACb,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAAEG,IAAI,IAAKR,YAAY,CAACQ,IAAI,CAAC,CAAC;IAC3C;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,SAAS,GAAIC,MAAM,IAAK;IAC1BpB,YAAY,CAAEqB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACD,MAAM,GAAGC,IAAI,CAACD,MAAM,CAAC,GAAG;IAAE,CAAC,CAAC,CAAC;IAEjE,IAAIP,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACpCb,KAAK,CAAC,iCAAiC,EAAE;QACrCc,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACLC,MAAM,EAAE,kBAAkB;UAC1B,YAAY,EAAG,GAAEJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,EAAC;UACrD,cAAc,EAAE;QACpB,CAAC;QACDI,IAAI,EAAEI,IAAI,CAACC,SAAS,CAAC;UAAEH,MAAM,EAAEA;QAAO,CAAC;MAC3C,CAAC,CAAC,CACGf,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAKC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC,CAAC;IAC1C;EACJ,CAAC;EAED,MAAMgB,cAAc,GAAIJ,MAAM,IAAK;IAC/BpB,YAAY,CAAEqB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAE,CAACD,MAAM,GAAGC,IAAI,CAACD,MAAM,CAAC,GAAG;IAAE,CAAC,CAAC,CAAC;IACjE,IAAIP,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;MACpCb,KAAK,CAAC,sCAAsC,EAAE;QAC1Cc,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACLC,MAAM,EAAE,kBAAkB;UAC1B,YAAY,EAAG,GAAEJ,YAAY,CAACC,OAAO,CAAC,YAAY,CAAE,EAAC;UACrD,cAAc,EAAE;QACpB,CAAC;QACDI,IAAI,EAAEI,IAAI,CAACC,SAAS,CAAC;UAAEH,MAAM,EAAEA;QAAO,CAAC;MAC3C,CAAC,CAAC,CACGf,IAAI,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACnCF,IAAI,CAAEG,IAAI,IAAKC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC,CAAC;IAC1C;EACJ,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,MAAMC,IAAI,IAAI5B,SAAS,EAAE;MAC1B,IAAIA,SAAS,CAAC4B,IAAI,CAAC,GAAG,CAAC,EAAE;QACrB,IAAIC,QAAQ,GAAG/B,WAAW,CAACgC,IAAI,CAAEC,OAAO,IAAKA,OAAO,CAACC,EAAE,KAAKC,MAAM,CAACL,IAAI,CAAC,CAAC;QACzE,IAAIC,QAAQ,EAAE;UACVF,WAAW,IAAIE,QAAQ,CAACK,SAAS,GAAGlC,SAAS,CAAC4B,IAAI,CAAC;QACvD;MACJ;IACJ;IACA,OAAOD,WAAW;EACtB,CAAC;EAED,MAAMQ,iBAAiB,GAAGA,CAAA,KAAM;IAC5B,IAAIC,SAAS,GAAG,CAAC;IACjB,KAAK,MAAMR,IAAI,IAAI5B,SAAS,EAAE;MAC1B,IAAIA,SAAS,CAAC4B,IAAI,CAAC,GAAG,CAAC,EAAE;QACrBQ,SAAS,IAAIpC,SAAS,CAAC4B,IAAI,CAAC;MAChC;IACJ;IACA,OAAOQ,SAAS;EACpB,CAAC;EAED,MAAMC,YAAY,GAAG;IACjBF,iBAAiB;IACjBT,kBAAkB;IAClB5B,WAAW;IACXE,SAAS;IACToB,SAAS;IACTK;EACJ,CAAC;EAED,oBACInC,OAAA,CAACC,WAAW,CAAC+C,QAAQ;IAACC,KAAK,EAAEF,YAAa;IAAAG,QAAA,EACrC5C,KAAK,CAAC4C;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAE/B,CAAC;AAAC/C,EAAA,CArGIF,mBAAmB;AAAAkD,EAAA,GAAnBlD,mBAAmB;AAuGzB,eAAeA,mBAAmB;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}