{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\e-Commerce\\\\intern\\\\src\\\\Pages\\\\LoginSignUp.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport './CSS/LoginSignUp.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoginSignUp = () => {\n  _s();\n  const [state, setState] = useState(\"Login\");\n  const [formData, setFormData] = useState({\n    username: \"\",\n    password: \"\",\n    email: \"\"\n  });\n  const changeHandler = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const login = async () => {\n    console.log(\"Login Function Executed\", formData);\n    let responseData;\n    await fetch('http://localhost:3000/login', {\n      method: 'POST',\n      headers: {\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(formData)\n    }).then(async response => {\n      responseData = await response.json();\n    });\n    if (responseData.success) {\n      localStorage.setItem('auth-token', responseData.token);\n      window.location.replace(\"/\");\n    } else {\n      alert(responseData.errors);\n    }\n  };\n  const signup = async () => {\n    console.log(\"Signup Function Executed\", formData);\n    let responseData;\n    await fetch('http://localhost:3000/signup', {\n      method: 'POST',\n      headers: {\n        Accept: 'application/json',\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(formData)\n    }).then(async response => {\n      responseData = await response.json();\n    });\n    if (responseData.success) {\n      localStorage.setItem('auth-token', responseData.token);\n      window.location.replace(\"/\");\n    } else {\n      alert(responseData.errors);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"loginsignup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loginsignup-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: state\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), state === \"Sign Up\" && /*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Sign Up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 33\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loginsignup-fields\",\n        children: [state === \"Sign Up\" ? /*#__PURE__*/_jsxDEV(\"input\", {\n          name: \"username\",\n          value: formData.username,\n          onChange: changeHandler,\n          type: \"text\",\n          placeholder: \"Your Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 13\n        }, this) : null, /*#__PURE__*/_jsxDEV(\"input\", {\n          name: \"email\",\n          value: formData.email,\n          onChange: changeHandler,\n          type: \"email\",\n          placeholder: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          name: \"password\",\n          value: formData.password,\n          onChange: changeHandler,\n          type: \"password\",\n          placeholder: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          state === \"Login\" ? login() : signup();\n        },\n        children: \"Continue\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), state === \"Sign Up\" ? /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"loginsignup-login\",\n        children: [\"Already have an account?\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => setState(\"Login\"),\n          children: \"Login here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"loginsignup-login\",\n        children: [\"Create an account?\", \" \", /*#__PURE__*/_jsxDEV(\"span\", {\n          onClick: () => setState(\"Sign Up\"),\n          children: \"Click here\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loginsignup-agree\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          name: \"\",\n          id: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"By Continuing, I agree to the terms of use & privacy policy.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n};\n_s(LoginSignUp, \"twkFj56BYz8x8R6iLVXnFFDNCzo=\");\n_c = LoginSignUp;\nexport default LoginSignUp;\nvar _c;\n$RefreshReg$(_c, \"LoginSignUp\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "LoginSignUp", "_s", "state", "setState", "formData", "setFormData", "username", "password", "email", "<PERSON><PERSON><PERSON><PERSON>", "e", "target", "name", "value", "login", "console", "log", "responseData", "fetch", "method", "headers", "Accept", "body", "JSON", "stringify", "then", "response", "json", "success", "localStorage", "setItem", "token", "window", "location", "replace", "alert", "errors", "signup", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onChange", "type", "placeholder", "onClick", "id", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Pages/LoginSignUp.jsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport './CSS/LoginSignUp.css';\r\n\r\nconst LoginSignUp = () => {\r\n  const [state, setState] = useState(\"Login\");\r\n  const [formData, setFormData] = useState({\r\n    username: \"\",\r\n    password: \"\",\r\n    email: \"\"\r\n  });\r\n\r\n  const changeHandler = (e) => {\r\n    setFormData({ ...formData, [e.target.name]: e.target.value });\r\n  };\r\n\r\n  const login = async () => {\r\n    console.log(\"Login Function Executed\", formData);\r\n    let responseData;\r\n    await fetch('http://localhost:3000/login', {\r\n      method: 'POST',\r\n      headers: {\r\n        Accept: 'application/json',\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(formData),\r\n    }).then(async (response) => {\r\n      responseData = await response.json();\r\n    });\r\n\r\n    if (responseData.success) {\r\n      localStorage.setItem('auth-token', responseData.token);\r\n      window.location.replace(\"/\");\r\n    } else {\r\n      alert(responseData.errors);\r\n    }\r\n  };\r\n\r\n  const signup = async () => {\r\n    console.log(\"Signup Function Executed\", formData);\r\n    let responseData;\r\n    await fetch('http://localhost:3000/signup', {\r\n      method: 'POST',\r\n      headers: {\r\n        Accept: 'application/json',\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify(formData),\r\n    }).then(async (response) => {\r\n      responseData = await response.json();\r\n    });\r\n\r\n    if (responseData.success) {\r\n      localStorage.setItem('auth-token', responseData.token);\r\n      window.location.replace(\"/\");\r\n    } else {\r\n      alert(responseData.errors);\r\n    }\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"loginsignup\">\r\n      <div className=\"loginsignup-container\">\r\n        <h1>{state}</h1>\r\n        {state === \"Sign Up\" && <h1>Sign Up</h1>}\r\n        <div className=\"loginsignup-fields\">\r\n          {state === \"Sign Up\" ? (\r\n            <input\r\n              name=\"username\"\r\n              value={formData.username}\r\n              onChange={changeHandler}\r\n              type=\"text\"\r\n              placeholder=\"Your Name\"\r\n            />\r\n          ) : null}\r\n          <input\r\n            name=\"email\"\r\n            value={formData.email}\r\n            onChange={changeHandler}\r\n            type=\"email\"\r\n            placeholder=\"Email Address\"\r\n          />\r\n          <input\r\n            name=\"password\"\r\n            value={formData.password}\r\n            onChange={changeHandler}\r\n            type=\"password\"\r\n            placeholder=\"Password\"\r\n          />\r\n        </div>\r\n        <button onClick={() => { state === \"Login\" ? login() : signup() }}>\r\n          Continue\r\n        </button>\r\n        {state === \"Sign Up\" ? (\r\n          <p className=\"loginsignup-login\">\r\n            Already have an account?{\" \"}\r\n            <span onClick={() => setState(\"Login\")}>Login here</span>\r\n          </p>\r\n        ) : (\r\n          <p className=\"loginsignup-login\">\r\n            Create an account?{\" \"}\r\n            <span onClick={() => setState(\"Sign Up\")}>Click here</span>\r\n          </p>\r\n        )}\r\n\r\n        <div className=\"loginsignup-agree\">\r\n          <input type=\"checkbox\" name=\"\" id=\"\" />\r\n          <p>\r\n            By Continuing, I agree to the terms of use & privacy policy.\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LoginSignUp;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGN,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC;IACvCS,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,aAAa,GAAIC,CAAC,IAAK;IAC3BL,WAAW,CAAC;MAAE,GAAGD,QAAQ;MAAE,CAACM,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAAM,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMC,KAAK,GAAG,MAAAA,CAAA,KAAY;IACxBC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEZ,QAAQ,CAAC;IAChD,IAAIa,YAAY;IAChB,MAAMC,KAAK,CAAC,6BAA6B,EAAE;MACzCC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACPC,MAAM,EAAE,kBAAkB;QAC1B,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpB,QAAQ;IAC/B,CAAC,CAAC,CAACqB,IAAI,CAAC,MAAOC,QAAQ,IAAK;MAC1BT,YAAY,GAAG,MAAMS,QAAQ,CAACC,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,IAAIV,YAAY,CAACW,OAAO,EAAE;MACxBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEb,YAAY,CAACc,KAAK,CAAC;MACtDC,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC;IAC9B,CAAC,MAAM;MACLC,KAAK,CAAClB,YAAY,CAACmB,MAAM,CAAC;IAC5B;EACF,CAAC;EAED,MAAMC,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzBtB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEZ,QAAQ,CAAC;IACjD,IAAIa,YAAY;IAChB,MAAMC,KAAK,CAAC,8BAA8B,EAAE;MAC1CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QACPC,MAAM,EAAE,kBAAkB;QAC1B,cAAc,EAAE;MAClB,CAAC;MACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACpB,QAAQ;IAC/B,CAAC,CAAC,CAACqB,IAAI,CAAC,MAAOC,QAAQ,IAAK;MAC1BT,YAAY,GAAG,MAAMS,QAAQ,CAACC,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,IAAIV,YAAY,CAACW,OAAO,EAAE;MACxBC,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEb,YAAY,CAACc,KAAK,CAAC;MACtDC,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC;IAC9B,CAAC,MAAM;MACLC,KAAK,CAAClB,YAAY,CAACmB,MAAM,CAAC;IAC5B;EACF,CAAC;EAGD,oBACErC,OAAA;IAAKuC,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC1BxC,OAAA;MAAKuC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCxC,OAAA;QAAAwC,QAAA,EAAKrC;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACfzC,KAAK,KAAK,SAAS,iBAAIH,OAAA;QAAAwC,QAAA,EAAI;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxC5C,OAAA;QAAKuC,SAAS,EAAC,oBAAoB;QAAAC,QAAA,GAChCrC,KAAK,KAAK,SAAS,gBAClBH,OAAA;UACEa,IAAI,EAAC,UAAU;UACfC,KAAK,EAAET,QAAQ,CAACE,QAAS;UACzBsC,QAAQ,EAAEnC,aAAc;UACxBoC,IAAI,EAAC,MAAM;UACXC,WAAW,EAAC;QAAW;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,GACA,IAAI,eACR5C,OAAA;UACEa,IAAI,EAAC,OAAO;UACZC,KAAK,EAAET,QAAQ,CAACI,KAAM;UACtBoC,QAAQ,EAAEnC,aAAc;UACxBoC,IAAI,EAAC,OAAO;UACZC,WAAW,EAAC;QAAe;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACF5C,OAAA;UACEa,IAAI,EAAC,UAAU;UACfC,KAAK,EAAET,QAAQ,CAACG,QAAS;UACzBqC,QAAQ,EAAEnC,aAAc;UACxBoC,IAAI,EAAC,UAAU;UACfC,WAAW,EAAC;QAAU;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACN5C,OAAA;QAAQgD,OAAO,EAAEA,CAAA,KAAM;UAAE7C,KAAK,KAAK,OAAO,GAAGY,KAAK,CAAC,CAAC,GAAGuB,MAAM,CAAC,CAAC;QAAC,CAAE;QAAAE,QAAA,EAAC;MAEnE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRzC,KAAK,KAAK,SAAS,gBAClBH,OAAA;QAAGuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,0BACP,EAAC,GAAG,eAC5BxC,OAAA;UAAMgD,OAAO,EAAEA,CAAA,KAAM5C,QAAQ,CAAC,OAAO,CAAE;UAAAoC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,gBAEJ5C,OAAA;QAAGuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAAC,oBACb,EAAC,GAAG,eACtBxC,OAAA;UAAMgD,OAAO,EAAEA,CAAA,KAAM5C,QAAQ,CAAC,SAAS,CAAE;UAAAoC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CACJ,eAED5C,OAAA;QAAKuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCxC,OAAA;UAAO8C,IAAI,EAAC,UAAU;UAACjC,IAAI,EAAC,EAAE;UAACoC,EAAE,EAAC;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvC5C,OAAA;UAAAwC,QAAA,EAAG;QAEH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA/GID,WAAW;AAAAiD,EAAA,GAAXjD,WAAW;AAiHjB,eAAeA,WAAW;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}