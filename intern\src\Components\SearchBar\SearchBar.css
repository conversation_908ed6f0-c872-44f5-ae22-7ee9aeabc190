.search-bar-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto 2rem;
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    padding: 0 20px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-input-wrapper:focus-within {
    border-color: #ff4141;
    box-shadow: 0 4px 20px rgba(255, 65, 65, 0.2);
}

.search-icon {
    color: #666;
    margin-right: 10px;
    font-size: 18px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    padding: 15px 0;
    font-size: 16px;
    background: transparent;
}

.search-input::placeholder {
    color: #999;
}

.clear-icon {
    color: #999;
    cursor: pointer;
    margin-left: 10px;
    font-size: 16px;
    transition: color 0.3s ease;
}

.clear-icon:hover {
    color: #ff4141;
}

.filter-toggle {
    background: #ff4141;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    margin-left: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-toggle:hover,
.filter-toggle.active {
    background: #e03131;
    transform: scale(1.05);
}

.filter-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 15px;
    padding: 20px;
    margin-top: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-group {
    margin-bottom: 15px;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #ff4141;
}

.price-inputs {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-inputs input {
    flex: 1;
}

.price-inputs span {
    color: #666;
    font-weight: 500;
}

.filter-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.reset-btn {
    background: #f8f9fa;
    border: 1px solid #ddd;
    color: #666;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.reset-btn:hover {
    background: #e9ecef;
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .search-bar-container {
        margin: 0 1rem 1.5rem;
    }
    
    .search-input-wrapper {
        padding: 0 15px;
    }
    
    .search-input {
        padding: 12px 0;
        font-size: 14px;
    }
    
    .filter-panel {
        padding: 15px;
    }
    
    .price-inputs {
        flex-direction: column;
        gap: 8px;
    }
    
    .price-inputs span {
        display: none;
    }
}
