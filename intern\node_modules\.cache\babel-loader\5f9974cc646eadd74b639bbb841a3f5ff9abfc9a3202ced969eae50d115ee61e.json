{"ast": null, "code": "import p1_img from \"./product_12.png\";\nimport p2_img from \"./product_35.png\";\nimport p3_img from \"./product_14.png\";\nimport p4_img from \"./product_8.png\";\nimport p5_img from \"./product_15.png\";\nimport p6_img from \"./product_2.png\";\nimport p7_img from \"./product_17.png\";\nimport p8_img from \"./product_28.png\";\nlet new_collections = [{\n  id: 12,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p1_img,\n  new_price: 50.0,\n  old_price: 80.5\n}, {\n  id: 35,\n  name: \"Boys Orange Colourblocked Hooded Sweatshirt\",\n  image: p2_img,\n  new_price: 85.0,\n  old_price: 120.5\n}, {\n  id: 14,\n  name: \"Men Green Solid Zippered Full-Zip Slim Fit Bomber Jacket\",\n  image: p3_img,\n  new_price: 60.0,\n  old_price: 100.5\n}, {\n  id: 8,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p4_img,\n  new_price: 100.0,\n  old_price: 150.0\n}, {\n  id: 15,\n  name: \"Men Green Solid Zippered Full-Zip Slim Fit Bomber Jacket\",\n  image: p5_img,\n  new_price: 50.0,\n  old_price: 80.5\n}, {\n  id: 2,\n  name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\n  image: p6_img,\n  new_price: 85.0,\n  old_price: 120.5\n}, {\n  id: 17,\n  name: \"Men Green Solid Zippered Full-Zip Slim Fit Bomber Jacket\",\n  image: p7_img,\n  new_price: 60.0,\n  old_price: 100.5\n}, {\n  id: 28,\n  name: \"Boys Orange Colourblocked Hooded Sweatshirt\",\n  image: p8_img,\n  new_price: 100.0,\n  old_price: 150.0\n}];\nexport default new_collections;", "map": {"version": 3, "names": ["p1_img", "p2_img", "p3_img", "p4_img", "p5_img", "p6_img", "p7_img", "p8_img", "new_collections", "id", "name", "image", "new_price", "old_price"], "sources": ["C:/Users/<USER>/Desktop/e-Commerce/intern/src/Components/Asset/new_collections.js"], "sourcesContent": ["import p1_img from \"./product_12.png\";\r\nimport p2_img from \"./product_35.png\";\r\nimport p3_img from \"./product_14.png\";\r\nimport p4_img from \"./product_8.png\";\r\nimport p5_img from \"./product_15.png\";\r\nimport p6_img from \"./product_2.png\";\r\nimport p7_img from \"./product_17.png\";\r\nimport p8_img from \"./product_28.png\";\r\n\r\nlet new_collections = [\r\n  {\r\n    id: 12,\r\n    name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image: p1_img,\r\n    new_price: 50.0,\r\n    old_price: 80.5,\r\n  },\r\n  {\r\n    id: 35,\r\n    name: \"Boys Orange Colourblocked Hooded Sweatshirt\",\r\n    image: p2_img,\r\n    new_price: 85.0,\r\n    old_price: 120.5,\r\n  },\r\n  {\r\n    id: 14,\r\n    name: \"Men Green Solid Zippered Full-Zip Slim Fit Bomber Jacket\",\r\n    image: p3_img,\r\n    new_price: 60.0,\r\n    old_price: 100.5,\r\n  },\r\n  {\r\n    id: 8,\r\n    name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image: p4_img,\r\n    new_price: 100.0,\r\n    old_price: 150.0,\r\n  },\r\n  {\r\n    id: 15,\r\n    name: \"Men Green Solid Zippered Full-Zip Slim Fit Bomber Jacket\",\r\n    image: p5_img,\r\n    new_price: 50.0,\r\n    old_price: 80.5,\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"Striped Flutter Sleeve Overlap Collar Peplum Hem Blouse\",\r\n    image: p6_img,\r\n    new_price: 85.0,\r\n    old_price: 120.5,\r\n  },\r\n  {\r\n    id: 17,\r\n    name: \"Men Green Solid Zippered Full-Zip Slim Fit Bomber Jacket\",\r\n    image: p7_img,\r\n    new_price: 60.0,\r\n    old_price: 100.5,\r\n  },\r\n  {\r\n    id: 28,\r\n    name: \"Boys Orange Colourblocked Hooded Sweatshirt\",\r\n    image: p8_img,\r\n    new_price: 100.0,\r\n    old_price: 150.0,\r\n  },\r\n];\r\n\r\nexport default new_collections;\r\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,kBAAkB;AAErC,IAAIC,eAAe,GAAG,CACpB;EACEC,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,yDAAyD;EAC/DC,KAAK,EAAEX,MAAM;EACbY,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,6CAA6C;EACnDC,KAAK,EAAEV,MAAM;EACbW,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,0DAA0D;EAChEC,KAAK,EAAET,MAAM;EACbU,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,yDAAyD;EAC/DC,KAAK,EAAER,MAAM;EACbS,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,0DAA0D;EAChEC,KAAK,EAAEP,MAAM;EACbQ,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,yDAAyD;EAC/DC,KAAK,EAAEN,MAAM;EACbO,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,0DAA0D;EAChEC,KAAK,EAAEL,MAAM;EACbM,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE;AACb,CAAC,EACD;EACEJ,EAAE,EAAE,EAAE;EACNC,IAAI,EAAE,6CAA6C;EACnDC,KAAK,EAAEJ,MAAM;EACbK,SAAS,EAAE,KAAK;EAChBC,SAAS,EAAE;AACb,CAAC,CACF;AAED,eAAeL,eAAe"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}