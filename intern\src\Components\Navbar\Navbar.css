.navbar {
    background-color: #ffffff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 32px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.dark-mode {
    background-color: #1a1a1a;
    color: #ffffff;
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-logo a {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
}

.nav-logo img {
    height: 40px;
    width: auto;
}

.nav-logo p {
    color: #171717;
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    transition: color 0.3s ease;
}

.navbar.dark-mode .nav-logo p {
    color: #ffffff;
}

/* Add this CSS to make the links more attractive */
.nav-menu {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 50px;
}

.nav-menu li {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 3px;
    cursor: pointer;
}

.nav-menu li a {
    text-decoration: none;
    color: #2f2f2f;
    font-size: 20px;
    font-weight: 900;
    transition: color 0.3s ease;

    /* Add a smooth color transition */
}

.nav-menu li a:hover {
    color: #ff5733;
    /* Change the color on hover */
}

/* Add this to style the horizontal rule */
.nav-menu li hr {
    width: 100%;
    border: 1px solid #626262;
    margin: 5px 0;
}

/* .nav-menu li a {
    list-style: none;
} */

.nav-menu hr {
    border: none;
    width: 80%;
    height: 3px;
    border-radius: 10px;
    background: #ff4141;

}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-icon-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.nav-icon-btn:hover {
    background: rgba(255, 65, 65, 0.1);
    color: #ff4141;
    transform: scale(1.1);
}

.navbar.dark-mode .nav-icon-btn {
    color: #ccc;
}

.navbar.dark-mode .nav-icon-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.nav-icon-link {
    text-decoration: none;
    color: inherit;
}

.nav-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
}

.nav-icon-container:hover {
    background: rgba(255, 65, 65, 0.1);
    transform: scale(1.1);
}

.count-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4141;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-form {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.search-container.active .search-form {
    opacity: 1;
    visibility: visible;
}

.search-input {
    padding: 8px 16px;
    border: 2px solid #ff4141;
    border-radius: 25px;
    outline: none;
    width: 200px;
    font-size: 14px;
    background: white;
    color: #333;
}

.navbar.dark-mode .search-input {
    background: #333;
    color: white;
    border-color: #ff4141;
}

.user-menu {
    position: relative;
}

.user-btn {
    background: #ff4141 !important;
    color: white !important;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-menu:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown a,
.user-dropdown button {
    display: block;
    width: 100%;
    padding: 8px 16px;
    text-decoration: none;
    color: #333;
    background: none;
    border: none;
    text-align: left;
    cursor: pointer;
    transition: background 0.3s ease;
}

.user-dropdown a:hover,
.user-dropdown button:hover {
    background: #f5f5f5;
}

.navbar.dark-mode .user-dropdown {
    background: #333;
    border-color: #555;
}

.navbar.dark-mode .user-dropdown a,
.navbar.dark-mode .user-dropdown button {
    color: white;
}

.navbar.dark-mode .user-dropdown a:hover,
.navbar.dark-mode .user-dropdown button:hover {
    background: #444;
}

.nav-login-cart {
    display: flex;
    align-items: center;
    gap: 15px;
}

.login-btn {
    background: #ff4141;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover {
    background: #e03131;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 65, 65, 0.3);
}

.nav-login-cart button {
    width: 157px;
    height: 58px;
    outline: none;
    border: 1px solid #7a7a7a;
    border-radius: 75px;
    color: #515151;
    font-size: 20px;
    font-weight: 500;
    background: white;
    cursor: pointer;

}

.nav-login-cart button:active {
    background: #f3f3f3;
}

.cart-count {
    width: 22px;
    height: 22px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -35px;
    margin-left: -55px;
    border-radius: 11px;
    font-size: 14px;
    background-color: red;
    color: white;

}

.nav-dropdown {
    display: none;

}

@media(max-width:1280px) {
    .navbar {
        padding: 12px 50px;


    }

    .nav-logo img {
        font-size: 25px;
    }

    .nav-logo p {
        font-size: 25px;
    }

    .nav-menu {
        gap: 30px;
        font-size: 16px;
    }

    .nav-login-cart {
        gap: 30px;
    }

    .nav-login-cart button {
        width: 120px;
        height: 45px;
        font-size: 16px;
    }

    .nav-cart-count {
        margin-left: -40px;
        font-size: 12px;

    }

}

@media(max-width:1024px) {
    .navbar {
        padding: 12px 30px;
    }

    .nav-menu {
        gap: 25px;
        font-size: 14px;

    }

    .nav-login-cart button {
        width: 80px;
        height: 35px;
        font-size: 14px;

    }

    .nav-login-cart img {
        width: 30px;
    }

    .nav-cart-count {
        width: 18px;
        height: 18px;
    }

}

@media(max-width:800px) {
    .navbar {
        padding: 10px 0px;
    }

    .nav-dropdown {
        display: block;
        width: 30px;
        rotate: -90deg;
        transition: 0.5s;
    }

    .nav-menu {
        display: none;
        height: 80px;
        width: 100%;
        position: absolute;
        background-color: white;
        justify-content: center;
        top: 50px;


    }

    .nav-menu-visible {
        display: flex;

    }

    .nav-dropdown.open {
        transform: rotate(90deg);
    }
}

@media(max-width:500px) {
    .navbar {
        padding: 8px 0px;
        gap: 0;

    }

    .nav-logo {
        transform: scale(0.8);
    }

    .nav-menu {
        height: 70px;
        top: 50px;
    }

    .nav-login-cart {
        text-transform: scale(0.8);
    }

}